{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 16129102281742393188, "deps": [[1884099982326826527, "cfg_aliases", false, 11123950833021712074]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-100bd5095f0fb898\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}