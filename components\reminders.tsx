"use client";

import type React from "react";

import { AuthDialog } from "@/components/auth-dialog";
import { SyncStatusIndicator } from "@/components/sync-status";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { WeeklyStatistics } from "@/components/weekly-statistics";
import type { Quote, Task } from "@/lib/storage/types";
import { cn } from "@/lib/utils";
import { Calendar, ChevronRight, Edit, RefreshCw, Save, Settings } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";

// Update the RemindersProps interface to include authentication-related props
interface RemindersProps {
	onDoneClick: () => void;
	tasks: Task[];
	remindersText: string;
	setRemindersText: (text: string) => void;
	weeklyStats: Record<string, Record<string, number>>;
	streak: number;
	syncStatus?: any;
	isOnline?: boolean;
	saving?: boolean;
	syncing?: boolean;
	error?: string | null;
	onSync?: () => void;
	quote: Quote | null;
	isLoadingQuote: boolean;
	onNextQuote: () => void;
	user: any | null;
	onSignIn: (email: string, password: string) => Promise<boolean>;
	onSignUp: (email: string, password: string) => Promise<boolean>;
	onSignOut: () => Promise<boolean>;
	isLocked?: boolean;
}

// Update the function signature to include the new props
export function Reminders({
	onDoneClick,
	tasks,
	remindersText,
	setRemindersText,
	weeklyStats,
	streak,
	syncStatus,
	isOnline,
	saving,
	syncing,
	error,
	onSync,
	quote,
	isLoadingQuote,
	onNextQuote,
	user,
	onSignIn,
	onSignUp,
	onSignOut,
	isLocked = false,
}: RemindersProps) {
	const [date, setDate] = useState("");
	const [isEditingReminders, setIsEditingReminders] = useState(false);
	const [isSettingsOpen, setIsSettingsOpen] = useState(false);
	const [isAuthDialogOpen, setIsAuthDialogOpen] = useState(false);
	const [authLoading, setAuthLoading] = useState(false);
	const [authError, setAuthError] = useState<string | null>(null);

	useEffect(() => {
		const today = new Date();
		const options: Intl.DateTimeFormatOptions = { weekday: "long", year: "numeric", month: "long", day: "numeric" };
		setDate(today.toLocaleDateString("en-US", options));
	}, []);

	// Add local content state to prevent conflicts with external updates
	const [localRemindersText, setLocalRemindersText] = useState<string>(remindersText);
	const [isTypingReminders, setIsTypingReminders] = useState(false);

	// Add refs to track updates and prevent conflicts
	const isUpdatingFromProps = useRef(false);
	const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const lastSavedRemindersText = useRef<string>("");

	// Update local content when reminders text changes from props
	useEffect(() => {
		if (!isTypingReminders && !isUpdatingFromProps.current) {
			setLocalRemindersText(remindersText);
			lastSavedRemindersText.current = remindersText;
		}
	}, [remindersText, isTypingReminders]);

	// Debounced save function to prevent excessive updates
	const debouncedSaveReminders = useCallback(
		(text: string) => {
			if (saveTimeoutRef.current) {
				clearTimeout(saveTimeoutRef.current);
			}

			saveTimeoutRef.current = setTimeout(() => {
				if (text === lastSavedRemindersText.current) return;

				isUpdatingFromProps.current = true;
				setRemindersText(text);
				lastSavedRemindersText.current = text;
				setIsTypingReminders(false);

				// Reset the flag after a short delay to allow state to settle
				setTimeout(() => {
					isUpdatingFromProps.current = false;
				}, 100);
			}, 300); // 300ms debounce
		},
		[setRemindersText]
	);

	// Optimized reminders text change handler
	const handleRemindersTextChange = useCallback(
		(e: React.ChangeEvent<HTMLTextAreaElement>) => {
			const newText = e.target.value;
			setLocalRemindersText(newText);
			setIsTypingReminders(true);
			debouncedSaveReminders(newText);
		},
		[debouncedSaveReminders]
	);

	// Cleanup timeout on unmount
	useEffect(() => {
		return () => {
			if (saveTimeoutRef.current) {
				clearTimeout(saveTimeoutRef.current);
			}
		};
	}, []);

	// Add handlers for auth actions
	const handleSignIn = async (email: string, password: string) => {
		setAuthLoading(true);
		setAuthError(null);
		try {
			const success = await onSignIn(email, password);
			if (!success) {
				setAuthError("Failed to sign in. Please check your credentials.");
			}
			return success;
		} catch (error) {
			setAuthError(error instanceof Error ? error.message : "An unknown error occurred");
			return false;
		} finally {
			setAuthLoading(false);
		}
	};

	const handleSignUp = async (email: string, password: string) => {
		setAuthLoading(true);
		setAuthError(null);
		try {
			const success = await onSignUp(email, password);
			if (!success) {
				setAuthError("Failed to create account. The email might already be in use.");
			}
			return success;
		} catch (error) {
			setAuthError(error instanceof Error ? error.message : "An unknown error occurred");
			return false;
		} finally {
			setAuthLoading(false);
		}
	};

	const handleSignOut = async () => {
		setAuthLoading(true);
		try {
			await onSignOut();
		} catch (error) {
			console.error("Sign out error:", error);
		} finally {
			setAuthLoading(false);
		}
	};

	const handleDoneClick = () => {
		onDoneClick(); // Simplified call
	};

	// Update the Dialog content to include authentication section
	return (
		<div className="h-full flex flex-col">
			<div className="flex items-center justify-between mb-5">
				<h2 className="text-xl font-semibold text-white">Reminders</h2>
				<div className="flex items-center gap-2 text-sm text-gray-400 leading-none">
					<Calendar className="h-4 w-4 mt-[1px]" />
					<span className="relative top-[2px]">{date}</span>
				</div>
			</div>

			<div className="flex flex-col gap-4 flex-1">
				<div className="bg-gradient-to-br from-gray-800/70 to-gray-800/50 backdrop-blur-sm rounded-xl p-5 w-full border border-gray-700/30 relative">
					<Button
						variant="ghost"
						size="icon"
						className="absolute top-1/2 right-2 -translate-y-1/2 h-7 w-7 text-gray-400 hover:text-teal-400 hover:bg-gray-800/80 z-10"
						onClick={onNextQuote}
						title="Next quote"
						disabled={isLoadingQuote}
					>
						{isLoadingQuote ? (
							<div className="animate-spin h-3 w-3 border border-gray-400 border-t-teal-400 rounded-full" />
						) : (
							<ChevronRight className="h-4 w-4" />
						)}
					</Button>

					{/* Prevent layout shift by maintaining consistent height */}
					<div className="min-h-[4rem] flex items-center">
						{isLoadingQuote ? (
							<div className="flex items-center justify-center w-full">
								<div className="animate-spin h-5 w-5 border-2 border-gray-500 border-t-teal-400 rounded-full" />
							</div>
						) : quote ? (
							<div className="w-full pr-8">
								<p className="text-xl font-bold text-teal-400 mb-2">{quote.translatedText}</p>
								<p className="text-sm text-gray-400">
									{quote.text} — <span className="italic">{quote.author}</span>
								</p>
							</div>
						) : (
							<div className="w-full pr-8">
								<p className="text-gray-500">Could not load quote.</p>
							</div>
						)}
					</div>
				</div>

				<div className="bg-gray-800/50 backdrop-blur-sm rounded-xl pt-3.5 pb-5 px-5 w-full border border-gray-700/30 relative">
					<Button
						variant="ghost"
						size="icon"
						className={cn(
							"absolute top-1/2 right-2 -translate-y-1/2 h-7 w-7 text-gray-400 hover:text-teal-400",
							isEditingReminders && "text-teal-400 bg-gray-700/50"
						)}
						onClick={() => setIsEditingReminders(!isEditingReminders)}
					>
						{isEditingReminders ? <Save className="h-3.5 w-3.5" /> : <Edit className="h-3.5 w-3.5" />}
					</Button>

					{isEditingReminders ? (
						<Textarea
							value={localRemindersText}
							onChange={handleRemindersTextChange}
							className="min-h-[120px] bg-gray-800/50 border-gray-700/50 resize-none scrollable-textarea notes-scrollbar"
							placeholder="Enter your reminders..."
							spellCheck={false}
							autoComplete="off"
						/>
					) : (
						<div className="text-sm text-gray-300 whitespace-pre-line mt-2">{localRemindersText}</div>
					)}
				</div>

				<div className="flex items-center gap-3">
					<div className="flex items-center justify-between bg-gray-800/50 backdrop-blur-sm rounded-xl p-4 flex-1 border border-gray-700/30">
						<p className="text-sm text-gray-400">Current Streak</p>
						<p className="text-sm font-medium text-teal-400">{streak} days</p>
					</div>
					<Button
						variant="ghost"
						className="h-[54px] w-[54px] rounded-xl bg-gray-800/50 backdrop-blur-sm hover:bg-gray-700/50 text-gray-400 hover:text-teal-400 p-0"
						onClick={() => setIsSettingsOpen(true)}
					>
						<Settings className="h-8 w-8" />
					</Button>
				</div>

				<Button
					size="lg"
					className={cn(
						"w-full bg-gradient-to-r from-teal-600 to-teal-500 hover:from-teal-500 hover:to-teal-400 text-white font-bold py-5 rounded-xl text-lg",
						isLocked &&
							"opacity-50 cursor-not-allowed from-gray-600 to-gray-500 hover:from-gray-600 hover:to-gray-500"
					)}
					onClick={isLocked ? undefined : handleDoneClick}
					disabled={isLocked}
					aria-disabled={isLocked}
					title={isLocked ? "Tasks already completed today. Check back tomorrow!" : "Mark all tasks as done"}
				>
					{isLocked ? "COMPLETED FOR TODAY" : "DONE"}
				</Button>

				<div className="flex-1" />
				<WeeklyStatistics weeklyData={weeklyStats} tasks={tasks} />
			</div>

			<Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
				<DialogContent className="bg-gray-900 text-gray-200 border-gray-800">
					<DialogHeader>
						<DialogTitle>Settings</DialogTitle>
					</DialogHeader>
					<div className="space-y-6 py-2">
						{/* Add User Authentication Section */}
						<div className="space-y-4 border-b border-gray-700 pb-6">
							<Label className="text-base font-medium">User Account</Label>
							{user ? (
								<div className="space-y-3">
									<div className="p-3 bg-gray-800/40 rounded-lg border border-gray-700/50">
										<p className="text-sm text-gray-300">Signed in as:</p>
										<p className="text-sm font-medium text-teal-400 truncate">{user.email}</p>
									</div>
									<Button
										onClick={handleSignOut}
										disabled={authLoading}
										size="sm"
										variant="outline"
										className="w-full bg-gray-800/50 hover:bg-gray-700/50 text-white hover:text-white border-gray-700/50"
									>
										{authLoading ? "Signing out..." : "Sign Out"}
									</Button>
								</div>
							) : (
								<div className="space-y-3">
									<div className="p-3 bg-gray-800/40 rounded-lg border border-gray-700/50">
										<p className="text-sm text-gray-300">Not signed in</p>
										<p className="text-xs text-gray-500">
											Sign in to sync your data across devices
										</p>
									</div>
									<Button
										onClick={() => setIsAuthDialogOpen(true)}
										size="sm"
										className="w-full bg-teal-600 hover:bg-teal-500"
									>
										Sign In / Create Account
									</Button>
								</div>
							)}
						</div>

						<div className="space-y-4 border-b border-gray-700 pb-6">
							<Label className="text-base font-medium">Data Sync</Label>
							<SyncStatusIndicator className="p-3 bg-gray-800/40 rounded-lg border border-gray-700/50" />
							{error && (
								<div className="text-red-400 text-xs bg-red-900/20 p-2 rounded-md border border-red-800/30">
									Error: {error}
								</div>
							)}
							<Button
								onClick={onSync}
								disabled={!isOnline || syncing || !user}
								size="sm"
								variant="outline"
								className="w-full mt-3 bg-gray-800/50 hover:bg-gray-700/50 text-white hover:text-white border-gray-700/50"
							>
								{syncing ? (
									<>
										<RefreshCw className="h-4 w-4 mr-2 animate-spin" /> Syncing...
									</>
								) : (
									"Sync All Data Now"
								)}
							</Button>
						</div>
					</div>
					<DialogFooter>
						<Button
							onClick={() => setIsSettingsOpen(false)}
							className="bg-teal-600 hover:bg-teal-500 text-white"
						>
							Close
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Add AuthDialog component */}
			<AuthDialog
				open={isAuthDialogOpen}
				onOpenChange={setIsAuthDialogOpen}
				onSignIn={handleSignIn}
				onSignUp={handleSignUp}
				loading={authLoading}
				error={authError}
			/>
		</div>
	);
}
