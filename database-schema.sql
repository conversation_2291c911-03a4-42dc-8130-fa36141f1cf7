-- Optimized Database Schema for Daily Planner App
-- Created for new Supabase project: northen-start

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types for better data validation
CREATE TYPE task_status AS ENUM ('pending', 'completed', 'archived');
CREATE TYPE sync_operation AS ENUM ('upload', 'download', 'merge');
CREATE TYPE sync_result AS ENUM ('success', 'error', 'partial');

-- =============================================
-- USER TASKS TABLE (Optimized)
-- =============================================
CREATE TABLE user_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    tasks JSONB NOT NULL DEFAULT '[]'::jsonb,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    version INTEGER NOT NULL DEFAULT 1,
    
    -- Constraints
    CONSTRAINT user_tasks_user_id_unique UNIQUE (user_id),
    CONSTRAINT user_tasks_tasks_is_array CHECK (jsonb_typeof(tasks) = 'array')
);

-- Indexes for performance
CREATE INDEX idx_user_tasks_user_id ON user_tasks(user_id);
CREATE INDEX idx_user_tasks_updated_at ON user_tasks(updated_at);
CREATE INDEX idx_user_tasks_tasks_gin ON user_tasks USING GIN (tasks);

-- =============================================
-- USER NOTES TABLE (Optimized)
-- =============================================
CREATE TABLE user_notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notes JSONB NOT NULL DEFAULT '[]'::jsonb,
    groups JSONB NOT NULL DEFAULT '[]'::jsonb,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    version INTEGER NOT NULL DEFAULT 1,
    
    -- Constraints
    CONSTRAINT user_notes_user_id_unique UNIQUE (user_id),
    CONSTRAINT user_notes_notes_is_array CHECK (jsonb_typeof(notes) = 'array'),
    CONSTRAINT user_notes_groups_is_array CHECK (jsonb_typeof(groups) = 'array')
);

-- Indexes for performance
CREATE INDEX idx_user_notes_user_id ON user_notes(user_id);
CREATE INDEX idx_user_notes_updated_at ON user_notes(updated_at);
CREATE INDEX idx_user_notes_notes_gin ON user_notes USING GIN (notes);
CREATE INDEX idx_user_notes_groups_gin ON user_notes USING GIN (groups);

-- =============================================
-- USER REMINDERS CONFIG TABLE (Optimized)
-- =============================================
CREATE TABLE user_reminders_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    text TEXT NOT NULL DEFAULT '',
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    version INTEGER NOT NULL DEFAULT 1,
    
    -- Constraints
    CONSTRAINT user_reminders_config_user_id_unique UNIQUE (user_id),
    CONSTRAINT user_reminders_config_text_length CHECK (length(text) <= 10000)
);

-- Indexes for performance
CREATE INDEX idx_user_reminders_config_user_id ON user_reminders_config(user_id);
CREATE INDEX idx_user_reminders_config_updated_at ON user_reminders_config(updated_at);

-- =============================================
-- USER STATISTICS TABLE (Optimized)
-- =============================================
CREATE TABLE user_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    weekly_stats JSONB NOT NULL DEFAULT '{}'::jsonb,
    streak INTEGER NOT NULL DEFAULT 0,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    version INTEGER NOT NULL DEFAULT 1,
    
    -- Constraints
    CONSTRAINT user_statistics_user_id_unique UNIQUE (user_id),
    CONSTRAINT user_statistics_weekly_stats_is_object CHECK (jsonb_typeof(weekly_stats) = 'object'),
    CONSTRAINT user_statistics_streak_positive CHECK (streak >= 0)
);

-- Indexes for performance
CREATE INDEX idx_user_statistics_user_id ON user_statistics(user_id);
CREATE INDEX idx_user_statistics_updated_at ON user_statistics(updated_at);
CREATE INDEX idx_user_statistics_weekly_stats_gin ON user_statistics USING GIN (weekly_stats);

-- =============================================
-- USER APP SETTINGS TABLE (Optimized)
-- =============================================
CREATE TABLE user_app_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    active_tab VARCHAR(50) NOT NULL DEFAULT 'timeline',
    current_quote_id INTEGER,
    current_quote_date DATE,
    last_completion_day DATE,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    version INTEGER NOT NULL DEFAULT 1,
    
    -- Constraints
    CONSTRAINT user_app_settings_user_id_unique UNIQUE (user_id),
    CONSTRAINT user_app_settings_active_tab_valid CHECK (active_tab IN ('timeline', 'notes', 'reminders', 'statistics')),
    CONSTRAINT user_app_settings_current_quote_id_positive CHECK (current_quote_id > 0)
);

-- Indexes for performance
CREATE INDEX idx_user_app_settings_user_id ON user_app_settings(user_id);
CREATE INDEX idx_user_app_settings_updated_at ON user_app_settings(updated_at);

-- =============================================
-- SYNC LOG TABLE (Enhanced)
-- =============================================
CREATE TABLE sync_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    section VARCHAR(50) NOT NULL,
    operation sync_operation NOT NULL,
    result sync_result NOT NULL,
    message TEXT,
    data_timestamp TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT sync_log_section_valid CHECK (section IN ('tasks', 'notes', 'reminders_config', 'statistics', 'app_settings'))
);

-- Indexes for performance
CREATE INDEX idx_sync_log_user_id ON sync_log(user_id);
CREATE INDEX idx_sync_log_created_at ON sync_log(created_at);
CREATE INDEX idx_sync_log_user_section ON sync_log(user_id, section);
CREATE INDEX idx_sync_log_result ON sync_log(result);

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on all tables
ALTER TABLE user_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_reminders_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_statistics ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_app_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_log ENABLE ROW LEVEL SECURITY;

-- User Tasks Policies
CREATE POLICY "Users can view their own tasks" ON user_tasks
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own tasks" ON user_tasks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own tasks" ON user_tasks
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own tasks" ON user_tasks
    FOR DELETE USING (auth.uid() = user_id);

-- User Notes Policies
CREATE POLICY "Users can view their own notes" ON user_notes
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own notes" ON user_notes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notes" ON user_notes
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notes" ON user_notes
    FOR DELETE USING (auth.uid() = user_id);

-- User Reminders Config Policies
CREATE POLICY "Users can view their own reminders config" ON user_reminders_config
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own reminders config" ON user_reminders_config
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reminders config" ON user_reminders_config
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reminders config" ON user_reminders_config
    FOR DELETE USING (auth.uid() = user_id);

-- User Statistics Policies
CREATE POLICY "Users can view their own statistics" ON user_statistics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own statistics" ON user_statistics
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own statistics" ON user_statistics
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own statistics" ON user_statistics
    FOR DELETE USING (auth.uid() = user_id);

-- User App Settings Policies
CREATE POLICY "Users can view their own app settings" ON user_app_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own app settings" ON user_app_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own app settings" ON user_app_settings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own app settings" ON user_app_settings
    FOR DELETE USING (auth.uid() = user_id);

-- Sync Log Policies
CREATE POLICY "Users can view their own sync logs" ON sync_log
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own sync logs" ON sync_log
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- =============================================
-- UTILITY FUNCTIONS
-- =============================================

-- Function to setup sync log RLS (for compatibility with existing code)
CREATE OR REPLACE FUNCTION setup_sync_log_rls()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- This function is for compatibility with existing code
    -- RLS policies are already set up above
    RAISE NOTICE 'Sync log RLS policies are already configured';
END;
$$;

-- Function to update version numbers on updates
CREATE OR REPLACE FUNCTION update_version_and_timestamp()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.version = OLD.version + 1;
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Create triggers for version updates
CREATE TRIGGER user_tasks_version_trigger
    BEFORE UPDATE ON user_tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_version_and_timestamp();

CREATE TRIGGER user_notes_version_trigger
    BEFORE UPDATE ON user_notes
    FOR EACH ROW
    EXECUTE FUNCTION update_version_and_timestamp();

CREATE TRIGGER user_reminders_config_version_trigger
    BEFORE UPDATE ON user_reminders_config
    FOR EACH ROW
    EXECUTE FUNCTION update_version_and_timestamp();

CREATE TRIGGER user_statistics_version_trigger
    BEFORE UPDATE ON user_statistics
    FOR EACH ROW
    EXECUTE FUNCTION update_version_and_timestamp();

CREATE TRIGGER user_app_settings_version_trigger
    BEFORE UPDATE ON user_app_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_version_and_timestamp();

-- =============================================
-- PERFORMANCE OPTIMIZATIONS
-- =============================================

-- Analyze tables for better query planning
ANALYZE user_tasks;
ANALYZE user_notes;
ANALYZE user_reminders_config;
ANALYZE user_statistics;
ANALYZE user_app_settings;
ANALYZE sync_log;
