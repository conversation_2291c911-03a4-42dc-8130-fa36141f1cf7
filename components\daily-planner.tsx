"use client";

import { Confetti } from "@/components/confetti";
import { DebugPanel } from "@/components/debug-panel";
import { Notes } from "@/components/notes";
import { Reminders } from "@/components/reminders";
import { Timeline } from "@/components/timeline";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useOptimizedSwipe } from "@/hooks/use-optimized-swipe";
import { useStorage } from "@/hooks/use-storage";
import { quotesService } from "@/lib/quotes-service";
import { statisticsService } from "@/lib/statistics-service";
import type { Group, Note, Quote, Task } from "@/lib/storage/types";
import { AlertCircle, RefreshCw } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

const TABS = ["timeline", "reminders", "notes"] as const;
type TabType = (typeof TABS)[number];

// Maximum time to show loading screen before showing recovery options
const MAX_LOADING_TIME = 15000; // 15 seconds

export default function DailyPlanner() {
	const [showConfetti, setShowConfetti] = useState(false);
	const isResettingTasks = useRef(false);
	const isDesktop = useMediaQuery("(min-width: 1024px)");
	const [activeTab, setActiveTab] = useState<TabType>("timeline");
	const [currentQuote, setCurrentQuote] = useState<Quote | null>(null);
	const [isLoadingQuote, setIsLoadingQuote] = useState(true);
	const [loadingTooLong, setLoadingTooLong] = useState(false);
	const loadingTimerRef = useRef<NodeJS.Timeout | null>(null);
	const [quoteInitialized, setQuoteInitialized] = useState(false);
	const [debugInfo, setDebugInfo] = useState<string>("No debug info yet");

	// Add a ref to track if we're initializing to prevent sync loops
	const isInitializing = useRef(true);
	const lastSavedActiveTab = useRef<string | null>(null);

	const {
		tasksData,
		notesData,
		remindersConfigData,
		statisticsData,
		appSettingsData,
		user,
		loading,
		savingSection,
		error,
		syncStatus,
		isOnline,
		saveTasks,
		saveNotes,
		saveRemindersConfig,
		saveStatistics,
		saveAppSettings,
		syncAllData,
		loadAllData,
		clearAllLocalData,
		signIn,
		signUp,
		signOut,
		resetLoadingState,
	} = useStorage();

	// Get lastCompletionDay from appSettingsData
	const lastCompletionDay = appSettingsData?.lastCompletionDay || null;

	// Check if tasks should be locked (completed today)
	const isTasksLocked = useMemo(() => {
		if (!lastCompletionDay) return false;

		const today = new Date().toISOString().split("T")[0];
		return lastCompletionDay === today;
	}, [lastCompletionDay]);

	// Set up loading timeout to show recovery UI if loading takes too long
	useEffect(() => {
		if (loading) {
			// Clear any existing timer
			if (loadingTimerRef.current) {
				clearTimeout(loadingTimerRef.current);
			}

			// Set a new timer
			loadingTimerRef.current = setTimeout(() => {
				console.warn(`⚠️ Loading has taken longer than ${MAX_LOADING_TIME}ms, showing recovery options`);
				setLoadingTooLong(true);
			}, MAX_LOADING_TIME);
		} else {
			// Clear timer when not loading
			if (loadingTimerRef.current) {
				clearTimeout(loadingTimerRef.current);
			}
			setLoadingTooLong(false);
		}

		// Cleanup on unmount
		return () => {
			if (loadingTimerRef.current) {
				clearTimeout(loadingTimerRef.current);
			}
		};
	}, [loading]);

	// Helper function to check if app settings have actually changed
	const hasAppSettingsChanged = useCallback(
		(current: AppSettingsData, newSettings: Partial<AppSettingsData>): boolean => {
			return Object.keys(newSettings).some((key) => {
				const typedKey = key as keyof AppSettingsData;
				return current[typedKey] !== newSettings[typedKey];
			});
		},
		[]
	);

	// Helper function to save app settings only if they've changed
	const saveAppSettingsIfChanged = useCallback(
		async (newSettings: AppSettingsData): Promise<boolean> => {
			if (!appSettingsData) return false;

			if (!hasAppSettingsChanged(appSettingsData, newSettings)) {
				console.log("📝 App settings unchanged, skipping save");
				return true; // Return true since no save was needed
			}

			console.log("📝 App settings changed, saving:", {
				old: appSettingsData,
				new: newSettings,
			});
			return await saveAppSettings(newSettings);
		},
		[appSettingsData, hasAppSettingsChanged, saveAppSettings]
	);

	const handleQuoteIdUpdate = useCallback(
		async (newQuoteId: number) => {
			if (appSettingsData) {
				const today = new Date().toISOString().split("T")[0];
				await saveAppSettingsIfChanged({
					...appSettingsData,
					currentQuoteId: newQuoteId,
					currentQuoteDate: today,
				});
			}
		},
		[appSettingsData, saveAppSettingsIfChanged]
	);

	// Add a function to force sync when there might be a discrepancy
	const forceSyncIfNeeded = useCallback(async () => {
		if (!user || !isOnline) return;

		// Check if there are pending changes that need to be synced
		const hasPendingChanges = Object.values(syncStatus.pendingChanges || {}).some(Boolean);

		// If there are pending changes or it's been more than 5 minutes since last sync
		const lastSyncTime = syncStatus.lastSync ? new Date(syncStatus.lastSync).getTime() : 0;
		const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;

		if (hasPendingChanges || lastSyncTime < fiveMinutesAgo) {
			console.log("Forcing sync due to pending changes or time since last sync");
			await syncAllData();
		}
	}, [user, isOnline, syncStatus, syncAllData]);

	// Effect to check for data discrepancies on initial load
	useEffect(() => {
		if (!loading && user && isOnline && isInitializing.current) {
			forceSyncIfNeeded();
			isInitializing.current = false;
		}
	}, [loading, user, isOnline, forceSyncIfNeeded]);

	// Effect to periodically check for data discrepancies
	useEffect(() => {
		if (!user || !isOnline) return;

		const checkInterval = setInterval(() => {
			forceSyncIfNeeded();
		}, 10 * 60 * 1000); // Check every 10 minutes

		return () => clearInterval(checkInterval);
	}, [user, isOnline, forceSyncIfNeeded]);

	// Effect to load the current quote on startup
	useEffect(() => {
		const loadCurrentQuote = async () => {
			// Only show loading state if we haven't initialized quotes yet
			if (!quoteInitialized) {
				setIsLoadingQuote(true);
			}

			try {
				const quote = await quotesService.getCurrentQuote(
					appSettingsData?.currentQuoteId,
					appSettingsData?.currentQuoteDate
				);
				setCurrentQuote(quote);
				setQuoteInitialized(true);

				// If this is a new day and quote progressed, save the new state
				if (quote && appSettingsData) {
					const today = new Date().toISOString().split("T")[0];
					if (appSettingsData.currentQuoteDate !== today || appSettingsData.currentQuoteId !== quote.id) {
						await handleQuoteIdUpdate(quote.id);
					}
				}
			} catch (error) {
				console.error("Failed to load current quote:", error);
				setQuoteInitialized(true); // Mark as initialized even on error
			} finally {
				setIsLoadingQuote(false);
			}
		};

		// Only load quote after appSettingsData is available and we haven't initialized yet
		if (appSettingsData && !quoteInitialized) {
			loadCurrentQuote();
		}
	}, [appSettingsData, handleQuoteIdUpdate, quoteInitialized]);

	// Initialize activeTab from appSettingsData once loaded (only once)
	useEffect(() => {
		if (appSettingsData && appSettingsData.activeTab && isInitializing.current) {
			setActiveTab(appSettingsData.activeTab as TabType);
			lastSavedActiveTab.current = appSettingsData.activeTab;
		}
	}, [appSettingsData]);

	// Separate effect to sync active tab changes (only when user changes it)
	useEffect(() => {
		// Don't sync during initialization or if the tab hasn't actually changed
		if (isInitializing.current || !appSettingsData || lastSavedActiveTab.current === activeTab) {
			return;
		}

		const syncActiveTab = async () => {
			try {
				const success = await saveAppSettingsIfChanged({ ...appSettingsData, activeTab });
				if (success) {
					lastSavedActiveTab.current = activeTab;
				}
			} catch (error) {
				console.error("Failed to sync active tab:", error);
			}
		};

		// Debounce the sync to avoid rapid calls
		const timeoutId = setTimeout(syncActiveTab, 500);
		return () => clearTimeout(timeoutId);
	}, [activeTab, appSettingsData, saveAppSettingsIfChanged]);

	// Effect to check for day changes - tasks automatically unlock when isTasksLocked becomes false
	// No need to actively reset lastCompletionDay, just let the date comparison handle it
	useEffect(() => {
		// Optional: Log when tasks become unlocked due to day change
		if (lastCompletionDay && !isTasksLocked) {
			const today = new Date().toISOString().split("T")[0];
			console.log(`🌅 Tasks unlocked - completion day was ${lastCompletionDay}, today is ${today}`);
		}
	}, [lastCompletionDay, isTasksLocked]);

	// Handle non-scrollable tabs for reminders and notes (timeline remains scrollable)
	useEffect(() => {
		const container = document.querySelector(".container");

		if (activeTab === "reminders" || activeTab === "notes") {
			document.body.classList.add("no-scroll");
			container?.classList.remove("timeline-active");
		} else if (activeTab === "timeline") {
			document.body.classList.remove("no-scroll");
			container?.classList.add("timeline-active");
		}

		// Cleanup on unmount
		return () => {
			document.body.classList.remove("no-scroll");
			container?.classList.remove("timeline-active");
		};
	}, [activeTab]);

	const handleTabChange = useCallback((newTab: TabType) => {
		setActiveTab(newTab);
	}, []);

	const handleNextQuote = useCallback(async () => {
		setIsLoadingQuote(true);
		try {
			const nextQuote = await quotesService.getNextQuote();
			setCurrentQuote(nextQuote);

			// Save the new quote ID to both local storage and Supabase
			if (nextQuote && appSettingsData) {
				// Don't await this to make the UI more responsive
				handleQuoteIdUpdate(nextQuote.id).catch(console.error);
			}
		} catch (error) {
			console.error("Failed to get next quote:", error);
		} finally {
			setIsLoadingQuote(false);
		}
	}, [appSettingsData, handleQuoteIdUpdate]);

	// Data extraction with defaults
	const currentTasks = tasksData?.tasks || [];
	const currentNotes = notesData?.notes || [];
	const currentGroups = notesData?.groups || [];
	const currentRemindersText = remindersConfigData?.text || "";
	const currentWeeklyStats = statisticsData?.weeklyStats || {};
	const currentStreak = statisticsData?.streak || 0;
	const statisticsHistory = statisticsData?.history || [];

	// Save handlers
	const handleTasksChange = useCallback(
		(newTasks: Task[]) => {
			saveTasks({ tasks: newTasks, updated_at: "" });
		},
		[saveTasks]
	);

	const handleNotesAndGroupsChange = useCallback(
		(newNotes: Note[], newGroups: Group[]) => {
			saveNotes({ notes: newNotes, groups: newGroups, updated_at: "" });
		},
		[saveNotes]
	);

	const handleRemindersTextChange = useCallback(
		(newText: string) => {
			saveRemindersConfig({ text: newText, updated_at: "" });
		},
		[saveRemindersConfig]
	);

	const handleDoneClick = useCallback(() => {
		if (!appSettingsData) return;

		// Get today's date in YYYY-MM-DD format
		const today = new Date().toISOString().split("T")[0];

		// Set the last completion day in app settings (only if it's different)
		saveAppSettingsIfChanged({
			...appSettingsData,
			lastCompletionDay: today,
		});

		// Calculate daily points from completed tasks
		const dailyStats = statisticsService.calculateDailyPoints(currentTasks);

		// Update weekly stats with today's data
		const updatedWeeklyStats = statisticsService.updateWeeklyStats(statisticsData?.weeklyStats || {}, dailyStats);

		// Check if all tasks are completed for streak calculation
		const allTasksCompleted = currentTasks.every((task) => task.completed);
		const updatedStreak = allTasksCompleted ? currentStreak + 1 : 0;

		// Save updated statistics
		saveStatistics({
			weeklyStats: updatedWeeklyStats,
			streak: updatedStreak,
			updated_at: "",
		});

		// Reset tasks with confetti only if all completed
		resetAllTasks(allTasksCompleted);
	}, [
		saveStatistics,
		saveAppSettingsIfChanged,
		appSettingsData,
		statisticsData?.weeklyStats,
		currentTasks,
		currentStreak,
	]);

	const resetAllTasks = useCallback(
		(shouldShowConfetti: boolean) => {
			const completedTasks = currentTasks.filter((task) => task.completed);
			if (completedTasks.length === 0) {
				// If no tasks were completed, no animation or confetti
				return;
			}

			isResettingTasks.current = true;
			if (shouldShowConfetti) {
				setShowConfetti(true);
			}

			// Create a copy of tasks to work with
			const updatedTasks = [...currentTasks];

			// Find completed tasks and their indices in the sorted order
			const sortedTasks = [...currentTasks].sort((a, b) => {
				const timeA = a.time.split(":").map(Number);
				const timeB = b.time.split(":").map(Number);
				const minutesA = timeA[0] * 60 + timeA[1];
				const minutesB = timeB[0] * 60 + timeB[1];
				return minutesA - minutesB;
			});

			const completedTaskIndices = sortedTasks
				.map((task, index) => ({ task, sortedIndex: index }))
				.filter(({ task }) => task.completed)
				.reverse(); // Start from bottom (last) task

			// Animate unchecking tasks sequentially from bottom to top
			completedTaskIndices.forEach(({ task }, animationIndex) => {
				setTimeout(() => {
					// Find the task in the original array and uncheck it
					const originalTaskIndex = updatedTasks.findIndex(
						(t) => t.time === task.time && t.label === task.label
					);
					if (originalTaskIndex !== -1) {
						updatedTasks[originalTaskIndex] = { ...updatedTasks[originalTaskIndex], completed: false };
						// Update the tasks state to trigger the visual animation
						handleTasksChange([...updatedTasks]);
					}
				}, animationIndex * 200); // 200ms delay between each task
			});

			// Stop confetti after all animations complete
			setTimeout(() => {
				setShowConfetti(false);
				isResettingTasks.current = false;
			}, completedTaskIndices.length * 200 + 1500); // Animation time + extra confetti time
		},
		[currentTasks, handleTasksChange]
	);

	// Swipe navigation
	const currentTabIndex = TABS.indexOf(activeTab);
	const handleSwipeLeft = useCallback(() => {
		if (currentTabIndex < TABS.length - 1) {
			handleTabChange(TABS[currentTabIndex + 1]);
		}
	}, [currentTabIndex, handleTabChange]);

	const handleSwipeRight = useCallback(() => {
		if (currentTabIndex > 0) {
			handleTabChange(TABS[currentTabIndex - 1]);
		}
	}, [currentTabIndex, handleTabChange]);

	const { swipeState, swipeHandlers } = useOptimizedSwipe({
		onSwipeLeft: handleSwipeLeft,
		onSwipeRight: handleSwipeRight,
		minSwipeDistance: 60,
		threshold: 0.2,
	});

	// Handle recovery actions
	const handleForceReload = useCallback(() => {
		window.location.reload();
	}, []);

	const handleResetLoadingState = useCallback(() => {
		resetLoadingState();
	}, [resetLoadingState]);

	const handleClearAndReload = useCallback(async () => {
		await clearAllLocalData();
		window.location.reload();
	}, [clearAllLocalData]);

	// Loading state with recovery options
	if (loading) {
		return (
			<div className="min-h-screen bg-gradient-to-br from-gray-950 to-gray-900 text-gray-200 flex flex-col items-center justify-center p-4">
				<div className="animate-pulse text-gray-400 mb-4">Loading Planner...</div>

				{loadingTooLong && (
					<div className="mt-8 max-w-md w-full">
						<div className="bg-red-900/20 border border-red-800/30 rounded-lg p-4 mb-4">
							<div className="flex items-center gap-2 mb-2">
								<AlertCircle className="h-5 w-5 text-red-400" />
								<h3 className="font-medium text-red-300">Loading is taking longer than expected</h3>
							</div>
							<p className="text-sm text-gray-300 mb-4">
								The application seems to be stuck in a loading state. You can try one of the following
								options:
							</p>

							<div className="flex flex-col gap-2">
								<Button
									variant="outline"
									onClick={handleResetLoadingState}
									className="bg-gray-800 hover:bg-gray-700 border-gray-700"
								>
									Continue Without Reloading
								</Button>

								<Button
									variant="outline"
									onClick={handleForceReload}
									className="bg-gray-800 hover:bg-gray-700 border-gray-700"
								>
									<RefreshCw className="h-4 w-4 mr-2" />
									Reload Page
								</Button>

								<Button variant="destructive" onClick={handleClearAndReload}>
									Clear Local Data & Reload
								</Button>
							</div>
						</div>

						<div className="text-xs text-gray-500">
							<p>If the problem persists, try clearing your browser cache or contact support.</p>
						</div>
					</div>
				)}
			</div>
		);
	}

	const isSavingAnySection = Object.values(savingSection).some(Boolean);

	const remindersComponent = (
		<Reminders
			onDoneClick={handleDoneClick}
			tasks={currentTasks}
			remindersText={currentRemindersText}
			setRemindersText={handleRemindersTextChange}
			weeklyStats={currentWeeklyStats}
			streak={currentStreak}
			syncStatus={syncStatus}
			isOnline={isOnline}
			saving={isSavingAnySection}
			syncing={syncStatus.syncInProgress}
			error={error}
			onSync={syncAllData}
			quote={currentQuote}
			isLoadingQuote={isLoadingQuote}
			onNextQuote={handleNextQuote}
			user={user}
			onSignIn={signIn}
			onSignUp={signUp}
			onSignOut={signOut}
			isLocked={isTasksLocked}
		/>
	);

	const timelineComponent = <Timeline tasks={currentTasks} setTasks={handleTasksChange} isLocked={isTasksLocked} />;
	const notesComponent = (
		<Notes
			initialNotes={currentNotes}
			initialGroups={currentGroups}
			appSettings={appSettingsData}
			onNotesAndGroupsChange={handleNotesAndGroupsChange}
			onAppSettingsChange={(data) => {
				setDebugInfo(`🔥 saveAppSettings called with lastSelectedNoteId: ${data.lastSelectedNoteId}`);
				return saveAppSettings(data);
			}}
		/>
	);

	return (
		<>
			{/* Debug Info Bar */}
			<div className="fixed top-0 left-0 right-0 bg-red-900/90 text-white text-xs p-2 z-50 font-mono">
				DEBUG: {debugInfo}
			</div>

			{isDesktop ? (
				<div className="container mx-auto py-8 px-6 grid grid-cols-1 lg:grid-cols-12 gap-6 min-h-screen relative">
					<div className="lg:col-span-4 bg-gray-900/50 backdrop-blur-sm rounded-2xl p-5 shadow-xl border border-gray-800/50">
						{timelineComponent}
					</div>
					<div className="lg:col-span-4 bg-gray-900/50 backdrop-blur-sm rounded-2xl p-5 shadow-xl border border-gray-800/50">
						{remindersComponent}
					</div>
					<div className="lg:col-span-4 bg-gray-900/50 backdrop-blur-sm rounded-2xl p-5 shadow-xl border border-gray-800/50">
						{notesComponent}
					</div>
				</div>
			) : (
				<div className="container mx-auto p-4 h-screen flex flex-col">
					<Tabs
						value={activeTab}
						onValueChange={handleTabChange as (value: string) => void}
						className="w-full flex flex-col flex-1"
					>
						<TabsList className="grid grid-cols-3 mb-4 bg-gray-800/70 p-1">
							{TABS.map((tab) => (
								<TabsTrigger
									key={tab}
									value={tab}
									className="data-[state=active]:bg-teal-600 data-[state=active]:text-white capitalize"
								>
									{tab}
								</TabsTrigger>
							))}
						</TabsList>
						<div
							className="flex-1 bg-gray-900/50 backdrop-blur-sm rounded-2xl p-5 shadow-xl border border-gray-800/50 relative overflow-hidden min-h-0"
							style={{
								transform: swipeState.isActive
									? `translateX(${
											swipeState.direction === "left"
												? -swipeState.progress * 8
												: swipeState.progress * 8
									  }px)`
									: "translateX(0)",
								transition: swipeState.isActive ? "none" : "transform 0.2s ease-out",
							}}
							{...swipeHandlers}
						>
							<TabsContent value="timeline" className="mt-0 h-full tab-content-timeline">
								{timelineComponent}
							</TabsContent>
							<TabsContent
								value="reminders"
								className="mt-0 h-full tab-content-reminders non-scrollable-tab"
							>
								{remindersComponent}
							</TabsContent>
							<TabsContent value="notes" className="mt-0 h-full tab-content-notes non-scrollable-tab">
								{notesComponent}
							</TabsContent>
						</div>
					</Tabs>
				</div>
			)}

			{/* Debug Panel - Only show if debug mode is enabled */}
			{process.env.NEXT_PUBLIC_DEBUG_MODE === "true" && (
				<DebugPanel
					user={user}
					loading={loading}
					error={error}
					tasksData={tasksData}
					notesData={notesData}
					remindersConfigData={remindersConfigData}
					statisticsData={statisticsData}
					appSettingsData={appSettingsData}
					syncStatus={syncStatus}
					isOnline={isOnline}
					onLoadAllData={loadAllData}
					onSyncAllData={syncAllData}
					onClearAllData={clearAllLocalData}
				/>
			)}

			<div className="fixed inset-0 pointer-events-none z-50">
				<Confetti active={showConfetti} isResetting={!isResettingTasks.current && showConfetti} />
			</div>
		</>
	);
}
