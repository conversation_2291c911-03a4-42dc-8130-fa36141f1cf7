"use client"

import { useEffect, useRef, useState } from "react"

interface ConfettiProps {
  active: boolean
  isResetting?: boolean
  colors?: string[]
}

export function Confetti({
  active,
  isResetting = false,
  colors = ["#14b8a6", "#0ea5e9", "#f59e0b", "#ec4899"],
}: ConfettiProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationFrameId = useRef<number>()
  const particles = useRef<any[]>([])
  const [resetKey, setResetKey] = useState(0)

  // Reset particles when isResetting is true
  useEffect(() => {
    if (isResetting) {
      particles.current = []
      setResetKey((prev) => prev + 1)
    }
  }, [isResetting])

  useEffect(() => {
    if (!active || !canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas size
    const setCanvasSize = () => {
      const parent = canvas.parentElement
      if (parent) {
        canvas.width = parent.offsetWidth
        canvas.height = parent.offsetHeight
      }
    }

    setCanvasSize()
    window.addEventListener("resize", setCanvasSize)

    // Create confetti particles only if we don't have any
    const createParticles = () => {
      if (particles.current.length === 0) {
        const particleCount = 50

        for (let i = 0; i < particleCount; i++) {
          particles.current.push({
            x: Math.random() * canvas.width,
            y: (Math.random() * canvas.height) / 2 - canvas.height / 2,
            size: Math.random() * 5 + 2,
            color: colors[Math.floor(Math.random() * colors.length)],
            speed: Math.random() * 3 + 1,
            angle: Math.random() * Math.PI * 2,
            rotation: Math.random() * 360,
            rotationSpeed: (Math.random() - 0.5) * 5,
            shape: Math.floor(Math.random() * 3), // 0: circle, 1: square, 2: triangle
          })
        }
      }
    }

    // Draw confetti
    const drawConfetti = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      particles.current.forEach((p) => {
        ctx.save()
        ctx.translate(p.x, p.y)
        ctx.rotate((p.rotation * Math.PI) / 180)
        ctx.fillStyle = p.color

        // Draw different shapes
        if (p.shape === 0) {
          // Circle
          ctx.beginPath()
          ctx.arc(0, 0, p.size, 0, Math.PI * 2)
          ctx.fill()
        } else if (p.shape === 1) {
          // Square
          ctx.fillRect(-p.size / 2, -p.size / 2, p.size, p.size)
        } else {
          // Triangle
          ctx.beginPath()
          ctx.moveTo(0, -p.size)
          ctx.lineTo(p.size, p.size)
          ctx.lineTo(-p.size, p.size)
          ctx.closePath()
          ctx.fill()
        }

        ctx.restore()

        // Update position
        p.y += p.speed
        p.x += Math.sin(p.angle) * 1.5
        p.rotation += p.rotationSpeed

        // Reset if out of bounds
        if (p.y > canvas.height) {
          p.y = -p.size
          p.x = Math.random() * canvas.width
        }
      })

      animationFrameId.current = requestAnimationFrame(drawConfetti)
    }

    // Start animation
    createParticles()
    drawConfetti()

    // Cleanup
    return () => {
      window.removeEventListener("resize", setCanvasSize)
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current)
      }
    }
  }, [active, colors, resetKey])

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 pointer-events-none z-50"
      style={{
        opacity: active ? 1 : 0,
        transition: "opacity 0.5s",
        zIndex: 50,
      }}
    />
  )
}
