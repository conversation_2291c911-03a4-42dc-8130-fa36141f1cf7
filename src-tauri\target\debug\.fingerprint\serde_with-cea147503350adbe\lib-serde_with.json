{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5290030462671737236, "path": 5224613343921948320, "deps": [[9689903380558560274, "serde", false, 15667921800837325527], [11690957875220028834, "serde_with_macros", false, 2422916462833085063], [16257276029081467297, "serde_derive", false, 4425384994465868478]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-cea147503350adbe\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}