"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectLabel,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import type { AppSettingsData, Group, Note } from "@/lib/storage/types"; // Import Group type
import { cn } from "@/lib/utils";
import { Edit, Pencil, Plus, PlusCircle, Settings, Trash2 } from "lucide-react";
import type React from "react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

// Define group colors (can be part of initialGroups or a default set)
const DEFAULT_GROUP_COLORS: Record<string, string> = {
	work: "bg-teal-500",
	personal: "bg-purple-500",
	ideas: "bg-amber-500",
	tasks: "bg-blue-500",
	other: "bg-gray-500",
};

const DEFAULT_GROUP_TEXT_COLORS: Record<string, string> = {
	work: "text-teal-300",
	personal: "text-purple-300",
	ideas: "text-amber-300",
	tasks: "text-blue-300",
	other: "text-gray-300",
};

type GroupIdType = string; // Group IDs are strings

interface NotesProps {
	initialNotes: Note[];
	initialGroups: Group[];
	appSettings: AppSettingsData | null;
	onNotesAndGroupsChange: (updatedNotes: Note[], updatedGroups: Group[]) => void;
	onAppSettingsChange: (updatedSettings: AppSettingsData) => void;
}

export function Notes({
	initialNotes,
	initialGroups,
	appSettings,
	onNotesAndGroupsChange,
	onAppSettingsChange,
}: NotesProps) {
	const [notes, setNotes] = useState<Note[]>(initialNotes);
	const [groups, setGroups] = useState<Group[]>(initialGroups);
	const [debugLogs, setDebugLogs] = useState<string[]>([]);
	const [showDebug, setShowDebug] = useState(false);

	// Add refs to track if we're in the middle of an update to prevent conflicts
	const isUpdatingFromProps = useRef(false);
	const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const lastSavedContent = useRef<string>("");

	// Effect to update internal state if props change (e.g., after sync)
	useEffect(() => {
		if (!isUpdatingFromProps.current) {
			setNotes(initialNotes);
		}
	}, [initialNotes]);

	useEffect(() => {
		if (!isUpdatingFromProps.current) {
			setGroups(initialGroups);
		}
	}, [initialGroups]);

	// Initialize selectedNoteId with saved value or first note
	const getInitialSelectedNoteId = () => {
		const savedNoteId = appSettings?.lastSelectedNoteId;
		if (savedNoteId && notes.find((note) => note.id === savedNoteId)) {
			return savedNoteId;
		}
		return notes[0]?.id || null;
	};

	const [selectedNoteId, setSelectedNoteId] = useState<string | null>(getInitialSelectedNoteId());

	// Update selected note when appSettings loads
	useEffect(() => {
		if (appSettings?.lastSelectedNoteId && notes.find((note) => note.id === appSettings.lastSelectedNoteId)) {
			setSelectedNoteId(appSettings.lastSelectedNoteId);
		}
	}, [appSettings?.lastSelectedNoteId, notes]);
	const [newNoteTitle, setNewNoteTitle] = useState("");
	const [newNoteGroup, setNewNoteGroup] = useState<GroupIdType>(groups[0]?.id || "personal");
	const [isCreatingNote, setIsCreatingNote] = useState(false);
	const [isRenaming, setIsRenaming] = useState(false);
	const [renameTitle, setRenameTitle] = useState("");
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [isGroupManagementOpen, setIsGroupManagementOpen] = useState(false);
	const [newGroupName, setNewGroupName] = useState("");
	const [newGroupColor, setNewGroupColor] = useState("#14b8a6"); // Default hex for teal
	const [editingGroupId, setEditingGroupId] = useState<string | null>(null);

	// Add local content state to prevent conflicts with external updates
	const [localContent, setLocalContent] = useState<string>("");
	const [isTyping, setIsTyping] = useState(false);

	const selectedNote = notes.find((note) => note.id === selectedNoteId);

	// Update local content when selected note changes
	useEffect(() => {
		if (selectedNote && !isTyping) {
			setLocalContent(selectedNote.content);
			lastSavedContent.current = selectedNote.content;
		}
	}, [selectedNote, isTyping]);

	// Debounced save function to prevent excessive updates
	const debouncedSave = useCallback(
		(content: string) => {
			if (saveTimeoutRef.current) {
				clearTimeout(saveTimeoutRef.current);
			}

			saveTimeoutRef.current = setTimeout(() => {
				if (!selectedNoteId || content === lastSavedContent.current) return;

				isUpdatingFromProps.current = true;
				const updatedNotes = notes.map((note) =>
					note.id === selectedNoteId ? { ...note, content, updatedAt: new Date().toISOString() } : note
				);
				setNotes(updatedNotes);
				onNotesAndGroupsChange(updatedNotes, groups);
				lastSavedContent.current = content;
				setIsTyping(false);

				// Reset the flag after a short delay to allow state to settle
				setTimeout(() => {
					isUpdatingFromProps.current = false;
				}, 100);
			}, 300); // 300ms debounce
		},
		[selectedNoteId, notes, groups, onNotesAndGroupsChange]
	);

	// Optimized content change handler
	const handleNoteContentChange = useCallback(
		(e: React.ChangeEvent<HTMLTextAreaElement>) => {
			const newContent = e.target.value;
			setLocalContent(newContent);
			setIsTyping(true);
			debouncedSave(newContent);
		},
		[debouncedSave]
	);

	// Cleanup timeout on unmount
	useEffect(() => {
		return () => {
			if (saveTimeoutRef.current) {
				clearTimeout(saveTimeoutRef.current);
			}
		};
	}, []);

	const getGroupColor = (groupId: string): string => {
		const group = groups.find((g) => g.id === groupId);
		return group?.color || DEFAULT_GROUP_COLORS.other;
	};

	const getGroupTextColor = (groupId: string): string => {
		const group = groups.find((g) => g.id === groupId);
		if (group) {
			// Handle hex colors (manually added groups)
			if (group.color.startsWith("#")) {
				// For hex colors, we'll use the same color
				// This ensures the text color matches the bullet point color
				return group.color;
			}
			// Handle Tailwind classes (default groups)
			const colorMatch = group.color.match(/bg-([a-z]+)-(\d+)/);
			if (colorMatch) {
				const colorName = colorMatch[1]; // e.g., "teal" from "bg-teal-500"
				return `text-${colorName}-300`;
			}
		}
		return DEFAULT_GROUP_TEXT_COLORS.other;
	};

	const groupedNotes = useMemo(() => {
		const groupsMap: Record<string, Note[]> = {};
		notes.forEach((note) => {
			const groupId = note.group || "other";
			if (!groupsMap[groupId]) groupsMap[groupId] = [];
			groupsMap[groupId].push(note);
		});
		return groupsMap;
	}, [notes]);

	// Function to save the last selected note
	const saveLastSelectedNote = useCallback(
		(noteId: string | null) => {
			const log1 = `🔥 saveLastSelectedNote called with: noteId=${noteId}, appSettings=${!!appSettings}`;
			console.log(log1);
			setDebugLogs((prev) => [...prev.slice(-4), log1]);

			if (appSettings && noteId) {
				const updatedSettings: AppSettingsData = {
					...appSettings,
					lastSelectedNoteId: noteId,
					updated_at: new Date().toISOString(),
				};
				const log2 = `🔥 Calling onAppSettingsChange with lastSelectedNoteId: ${updatedSettings.lastSelectedNoteId}`;
				console.log(log2);
				setDebugLogs((prev) => [...prev.slice(-4), log2]);
				onAppSettingsChange(updatedSettings);
			} else {
				const log3 = `🔥 NOT saving - appSettings: ${!!appSettings}, noteId: ${noteId}`;
				console.log(log3);
				setDebugLogs((prev) => [...prev.slice(-4), log3]);
			}
		},
		[appSettings, onAppSettingsChange]
	);

	const createNewNote = useCallback(() => {
		if (!newNoteTitle.trim()) return;
		const now = new Date().toISOString();
		const newNote: Note = {
			id: `note-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`, // More unique ID
			title: newNoteTitle,
			content: "",
			group: newNoteGroup,
			createdAt: now,
			updatedAt: now,
		};

		isUpdatingFromProps.current = true;
		const updatedNotes = [...notes, newNote];
		setNotes(updatedNotes);
		setSelectedNoteId(newNote.id);
		setNewNoteTitle("");
		setIsCreatingNote(false);
		setLocalContent("");
		lastSavedContent.current = "";
		onNotesAndGroupsChange(updatedNotes, groups);

		// Save the newly created note as the last selected
		saveLastSelectedNote(newNote.id);

		setTimeout(() => {
			isUpdatingFromProps.current = false;
		}, 100);
	}, [newNoteTitle, newNoteGroup, notes, groups, onNotesAndGroupsChange, saveLastSelectedNote]);

	const startRenameNote = useCallback(() => {
		if (!selectedNote) return;
		setRenameTitle(selectedNote.title);
		setIsRenaming(true);
	}, [selectedNote]);

	const renameNote = useCallback(() => {
		if (!renameTitle.trim() || !selectedNoteId) return;

		isUpdatingFromProps.current = true;
		const updatedNotes = notes.map((note) =>
			note.id === selectedNoteId ? { ...note, title: renameTitle, updatedAt: new Date().toISOString() } : note
		);
		setNotes(updatedNotes);
		setIsRenaming(false);
		onNotesAndGroupsChange(updatedNotes, groups);

		setTimeout(() => {
			isUpdatingFromProps.current = false;
		}, 100);
	}, [renameTitle, selectedNoteId, notes, groups, onNotesAndGroupsChange]);

	const deleteNote = useCallback(() => {
		if (!selectedNoteId) return;

		isUpdatingFromProps.current = true;
		const updatedNotes = notes.filter((note) => note.id !== selectedNoteId);
		setNotes(updatedNotes);
		const newSelectedId = updatedNotes[0]?.id || null;
		setSelectedNoteId(newSelectedId);
		setLocalContent(updatedNotes[0]?.content || "");
		lastSavedContent.current = updatedNotes[0]?.content || "";
		setIsDeleteDialogOpen(false);
		onNotesAndGroupsChange(updatedNotes, groups);

		setTimeout(() => {
			isUpdatingFromProps.current = false;
		}, 100);
	}, [selectedNoteId, notes, groups, onNotesAndGroupsChange]);

	const openGroupManagement = useCallback(() => {
		setIsGroupManagementOpen(true);
		setNewGroupName("");
		setNewGroupColor("#14b8a6");
		setEditingGroupId(null);
	}, []);

	const startEditGroup = useCallback((group: Group) => {
		setEditingGroupId(group.id);
		setNewGroupName(group.name);
		setNewGroupColor(group.color.startsWith("#") ? group.color : `#${getHexFromTailwindColor(group.color)}`);
	}, []);

	const saveGroup = useCallback(() => {
		if (!newGroupName.trim()) return;
		const now = new Date().toISOString();
		let updatedGroups: Group[];

		isUpdatingFromProps.current = true;
		if (editingGroupId) {
			updatedGroups = groups.map((group) =>
				group.id === editingGroupId
					? {
							...group,
							name: newGroupName,
							color: newGroupColor, // Always save the hex color directly
							updatedAt: now,
					  }
					: group
			);
		} else {
			const newGroupId = `${newGroupName.toLowerCase().replace(/\s+/g, "-")}-${Date.now()}`;
			const newGroupData: Group = {
				id: newGroupId,
				name: newGroupName,
				color: newGroupColor, // Always save the hex color directly
				createdAt: now,
				updatedAt: now,
			};
			updatedGroups = [...groups, newGroupData];
		}
		setGroups(updatedGroups);
		setNewGroupName("");
		setNewGroupColor("#14b8a6");
		setEditingGroupId(null);
		onNotesAndGroupsChange(notes, updatedGroups);

		setTimeout(() => {
			isUpdatingFromProps.current = false;
		}, 100);
	}, [newGroupName, newGroupColor, editingGroupId, groups, notes, onNotesAndGroupsChange]);

	const deleteGroup = useCallback(
		(groupId: string) => {
			if (groups.length <= 1 && groups.find((g) => g.id === groupId)?.id === "other") {
				alert("Cannot delete the default 'Other' group if it's the only one left.");
				return;
			}
			const defaultGroupId =
				groups.find((g) => g.id === "other")?.id || groups.filter((g) => g.id !== groupId)[0]?.id;
			if (!defaultGroupId) {
				alert("Cannot delete group: No fallback group available.");
				return;
			}

			isUpdatingFromProps.current = true;
			const updatedNotes = notes.map((note) =>
				note.group === groupId ? { ...note, group: defaultGroupId, updatedAt: new Date().toISOString() } : note
			);
			const updatedGroups = groups.filter((group) => group.id !== groupId);

			setNotes(updatedNotes);
			setGroups(updatedGroups);
			onNotesAndGroupsChange(updatedNotes, updatedGroups);

			setTimeout(() => {
				isUpdatingFromProps.current = false;
			}, 100);
		},
		[groups, notes, onNotesAndGroupsChange]
	);

	const changeNoteGroup = useCallback(
		(groupId: GroupIdType) => {
			if (!selectedNoteId) return;

			isUpdatingFromProps.current = true;
			const updatedNotes = notes.map((note) =>
				note.id === selectedNoteId ? { ...note, group: groupId, updatedAt: new Date().toISOString() } : note
			);
			setNotes(updatedNotes);
			onNotesAndGroupsChange(updatedNotes, groups);

			setTimeout(() => {
				isUpdatingFromProps.current = false;
			}, 100);
		},
		[selectedNoteId, notes, groups, onNotesAndGroupsChange]
	);

	// Optimized note selection handler
	const handleNoteSelection = useCallback(
		(noteId: string) => {
			// Save current content before switching if we were typing
			if (isTyping && saveTimeoutRef.current) {
				clearTimeout(saveTimeoutRef.current);
				if (selectedNoteId && localContent !== lastSavedContent.current) {
					const updatedNotes = notes.map((note) =>
						note.id === selectedNoteId
							? { ...note, content: localContent, updatedAt: new Date().toISOString() }
							: note
					);
					setNotes(updatedNotes);
					onNotesAndGroupsChange(updatedNotes, groups);
				}
			}

			setSelectedNoteId(noteId);
			setIsTyping(false);
			const newNote = notes.find((note) => note.id === noteId);
			if (newNote) {
				setLocalContent(newNote.content);
				lastSavedContent.current = newNote.content;
			}

			// Save the last selected note
			saveLastSelectedNote(noteId);
		},
		[isTyping, selectedNoteId, localContent, notes, groups, onNotesAndGroupsChange, saveLastSelectedNote]
	);

	function formatGroupName(groupId: string) {
		const group = groups.find((g) => g.id === groupId);
		return group?.name || "Other";
	}

	function getHexFromTailwindColor(colorClass: string) {
		// Example: "bg-teal-500" -> "14b8a6"
		const match = colorClass.match(/bg-([a-z]+)-(\d+)/);
		if (!match) return "6b7280"; // Default gray hex

		const colorName = match[1];
		// This is a simplified map. A real app might need a more comprehensive one or a utility.
		const nameToHex: Record<string, string> = {
			teal: "14b8a6",
			purple: "a855f7",
			amber: "f59e0b",
			blue: "3b82f6",
			gray: "6b7280",
			red: "ef4444",
			lime: "84cc16",
			cyan: "06b6d4",
			violet: "8b5cf6",
			pink: "ec4899",
		};
		return nameToHex[colorName] || "6b7280";
	}

	const availableColors = [
		{ name: "teal", hex: "#14b8a6" },
		{ name: "purple", hex: "#a855f7" },
		{ name: "amber", hex: "#f59e0b" },
		{ name: "blue", hex: "#3b82f6" },
		{ name: "gray", hex: "#6b7280" },
		{ name: "red", hex: "#ef4444" },
		{ name: "lime", hex: "#84cc16" },
		{ name: "cyan", hex: "#06b6d4" },
		{ name: "violet", hex: "#8b5cf6" },
		{ name: "pink", hex: "#ec4899" },
	];

	return (
		<div className="h-full grid grid-rows-[auto_auto_1fr] gap-5">
			{/* Header */}
			<div className="flex items-center justify-between">
				<h2 className="text-xl font-semibold text-white">Notes</h2>
				<div className="flex gap-2">
					<Button
						variant="outline"
						size="sm"
						className="text-red-300 border-red-500 bg-red-950/30 hover:bg-red-900/30"
						onClick={() => setShowDebug(!showDebug)}
					>
						DEBUG
					</Button>
					<Button
						variant="outline"
						size="sm"
						className="text-teal-300 border-teal-500 bg-teal-950/30 hover:bg-teal-900/30 hover:text-teal-200"
						onClick={() => setIsCreatingNote(true)}
					>
						<PlusCircle className="h-5 w-5 mr-1.5" />
						New Note
					</Button>
				</div>
			</div>

			{/* Controls */}
			<div>
				{isCreatingNote ? (
					<div className="space-y-3">
						<Input
							type="text"
							value={newNoteTitle}
							onChange={(e) => setNewNoteTitle(e.target.value)}
							placeholder="Note title..."
							className="flex h-10 w-full rounded-md border border-gray-700 bg-gray-800/70 backdrop-blur-sm"
						/>
						<Select value={newNoteGroup} onValueChange={(value) => setNewNoteGroup(value as GroupIdType)}>
							<SelectTrigger className="bg-gray-800/70 border-gray-700 backdrop-blur-sm text-gray-200">
								<SelectValue placeholder="Select a group" />
							</SelectTrigger>
							<SelectContent className="bg-gray-800 border-gray-700">
								{groups.map((group) => (
									<SelectItem
										key={group.id}
										value={group.id}
										className="text-gray-200 focus:text-white focus:bg-gray-700"
									>
										<div className="flex items-center gap-2">
											<div
												className={`w-3 h-3 rounded-full ${
													group.color.startsWith("#") ? "" : group.color
												}`}
												style={
													group.color.startsWith("#") ? { backgroundColor: group.color } : {}
												}
											></div>
											{group.name}
										</div>
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						<div className="flex gap-2">
							<Button
								onClick={createNewNote}
								className="bg-teal-600 hover:bg-teal-500 text-white border-0"
							>
								Create
							</Button>
							<Button
								variant="outline"
								onClick={() => setIsCreatingNote(false)}
								className="border-gray-700 bg-gray-800 text-gray-100 hover:bg-gray-700 hover:text-white"
							>
								Cancel
							</Button>
						</div>
					</div>
				) : (
					<div className="flex gap-2">
						<Select
							value={selectedNoteId || ""}
							onValueChange={handleNoteSelection}
							disabled={notes.length === 0}
						>
							<SelectTrigger className="bg-gray-800/70 border-gray-700 backdrop-blur-sm text-gray-200 h-10 flex-1">
								{selectedNote ? (
									<div className="flex items-center gap-2 truncate">
										<span className="truncate">{selectedNote.title}</span>
										<span className="text-xs flex items-center gap-1 whitespace-nowrap">
											<span
												className={
													getGroupTextColor(selectedNote.group).startsWith("#")
														? ""
														: getGroupTextColor(selectedNote.group)
												}
												style={
													getGroupTextColor(selectedNote.group).startsWith("#")
														? { color: getGroupTextColor(selectedNote.group) }
														: {}
												}
											>
												•
											</span>
											<span
												className={
													getGroupTextColor(selectedNote.group).startsWith("#")
														? ""
														: getGroupTextColor(selectedNote.group)
												}
												style={
													getGroupTextColor(selectedNote.group).startsWith("#")
														? { color: getGroupTextColor(selectedNote.group) }
														: {}
												}
											>
												{formatGroupName(selectedNote.group)}
											</span>
										</span>
									</div>
								) : (
									<SelectValue placeholder="No notes available" />
								)}
							</SelectTrigger>
							<SelectContent
								className="bg-gray-900 border-gray-700/50 max-h-[300px] p-0 overflow-hidden rounded-md shadow-lg"
								position="popper"
								sideOffset={5}
							>
								<div className="max-h-[300px] overflow-y-auto py-1 scrollbar-thin notes-scrollbar">
									{Object.entries(groupedNotes).map(([groupId, groupNotesList]) => {
										const group = groups.find((g) => g.id === groupId) || {
											id: groupId,
											name: formatGroupName(groupId),
											color: DEFAULT_GROUP_COLORS.other,
											createdAt: "",
											updatedAt: "",
										};
										const textColor = getGroupTextColor(groupId);
										return (
											<SelectGroup key={groupId} className="mb-0.5 last:mb-0">
												<SelectLabel
													className={cn(
														"flex items-center gap-2 py-1.5 px-3 m-0",
														textColor.startsWith("#") ? "" : textColor,
														"font-medium text-xs uppercase tracking-wider"
													)}
													style={textColor.startsWith("#") ? { color: textColor } : {}}
												>
													<div
														className={`w-2 h-2 rounded-full ${
															group.color.startsWith("#") ? "" : group.color
														}`}
														style={
															group.color.startsWith("#")
																? { backgroundColor: group.color }
																: {}
														}
													></div>
													<span>{group.name}</span>
												</SelectLabel>
												<div className="py-0.5">
													{groupNotesList.map((note) => (
														<SelectItem
															key={note.id}
															value={note.id}
															className={cn(
																"flex items-center gap-2 py-1.5 pl-6 pr-3 m-0 text-gray-200 cursor-pointer",
																"hover:bg-gray-800/50 focus:bg-gray-800/50 focus:text-white",
																"data-[highlighted]:bg-gray-800/50 data-[highlighted]:text-white",
																selectedNoteId === note.id ? "text-white" : ""
															)}
														>
															<span className="truncate pl-5">{note.title}</span>
														</SelectItem>
													))}
												</div>
											</SelectGroup>
										);
									})}
								</div>
							</SelectContent>
						</Select>
						<div>
							<Button
								variant="ghost"
								size="icon"
								className="h-10 w-10 text-gray-400 hover:text-teal-400 hover:bg-gray-800"
								onClick={startRenameNote}
								disabled={!selectedNote}
							>
								<Pencil className="h-4 w-4" />
								<span className="sr-only">Edit</span>
							</Button>
						</div>
					</div>
				)}
			</div>

			{/* Content Area */}
			<div className="min-h-0">
				{selectedNote ? (
					<Textarea
						key={selectedNoteId} // Force re-render when note changes to prevent stale state
						value={localContent}
						onChange={handleNoteContentChange}
						placeholder="Write your note here..."
						className="w-full h-full bg-gray-800/50 border-gray-700/50 resize-none rounded-xl p-4 backdrop-blur-sm text-gray-200 scrollable-textarea notes-scrollbar"
						spellCheck={false} // Disable spellcheck to prevent interference
						autoComplete="off" // Disable autocomplete
					/>
				) : (
					<div className="h-full flex items-center justify-center text-gray-500 bg-gray-800/30 rounded-xl border border-gray-700/30 backdrop-blur-sm">
						{notes.length === 0 ? "Create a note to get started" : "Select a note"}
					</div>
				)}
			</div>

			{/* Debug Window */}
			{showDebug && (
				<div className="fixed top-4 right-4 w-96 h-64 bg-black/90 border border-red-500 rounded-lg p-4 z-50 overflow-y-auto">
					<div className="text-red-400 font-mono text-xs">
						<div className="flex justify-between items-center mb-2">
							<span>DEBUG LOGS</span>
							<button onClick={() => setDebugLogs([])} className="text-red-300 hover:text-red-100">
								Clear
							</button>
						</div>
						{debugLogs.map((log, i) => (
							<div key={i} className="mb-1 break-words">
								{log}
							</div>
						))}
					</div>
				</div>
			)}

			{/* Dialogs */}
			<Dialog open={isRenaming} onOpenChange={setIsRenaming}>
				<DialogContent className="bg-gray-900 text-gray-200 border-gray-800">
					<DialogHeader>
						<DialogTitle>Edit Note</DialogTitle>
					</DialogHeader>
					<div className="space-y-4">
						<div>
							<Label htmlFor="rename-title">Title</Label>
							<Input
								id="rename-title"
								value={renameTitle}
								onChange={(e) => setRenameTitle(e.target.value)}
								className="bg-gray-800 border-gray-700 mt-1"
							/>
						</div>
						<div>
							<Label htmlFor="rename-group">Group</Label>
							<div className="flex gap-2 mt-1">
								<Select
									value={selectedNote?.group}
									onValueChange={(value) => changeNoteGroup(value as GroupIdType)}
								>
									<SelectTrigger className="bg-gray-800 border-gray-700 text-gray-200 flex-1">
										<div className="flex items-center gap-2">
											<div
												className={`w-3 h-3 rounded-full ${
													selectedNote
														? getGroupColor(selectedNote.group).startsWith("#")
															? ""
															: getGroupColor(selectedNote.group)
														: ""
												}`}
												style={
													selectedNote && getGroupColor(selectedNote.group).startsWith("#")
														? { backgroundColor: getGroupColor(selectedNote.group) }
														: {}
												}
											></div>
											<span>{selectedNote ? formatGroupName(selectedNote.group) : ""}</span>
										</div>
									</SelectTrigger>
									<SelectContent className="bg-gray-800 border-gray-700">
										{groups.map((group) => (
											<SelectItem
												key={group.id}
												value={group.id}
												className="text-gray-200 focus:text-white focus:bg-gray-700"
											>
												<div className="flex items-center gap-2">
													<div
														className={`w-3 h-3 rounded-full ${
															group.color.startsWith("#") ? "" : group.color
														}`}
														style={
															group.color.startsWith("#")
																? { backgroundColor: group.color }
																: {}
														}
													></div>
													{group.name}
												</div>
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<Button
									variant="ghost"
									className="h-[40px] w-[40px] rounded-xl bg-gray-800/50 backdrop-blur-sm hover:bg-gray-700/50 text-gray-400 hover:text-teal-400 p-0"
									onClick={openGroupManagement}
								>
									<Settings className="h-8 w-8" />
								</Button>
							</div>
						</div>
					</div>
					<DialogFooter className="flex justify-between">
						<Button
							variant="destructive"
							onClick={() => {
								setIsRenaming(false);
								setIsDeleteDialogOpen(true);
							}}
							className="bg-red-600 hover:bg-red-700 text-white"
						>
							Delete Note
						</Button>
						<div className="flex gap-2">
							<Button
								variant="outline"
								onClick={() => setIsRenaming(false)}
								className="border-gray-700 bg-gray-800 text-gray-100 hover:bg-gray-700 hover:text-white"
							>
								Cancel
							</Button>
							<Button onClick={renameNote} className="bg-teal-600 hover:bg-teal-500 text-white border-0">
								Save
							</Button>
						</div>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			<Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
				<DialogContent className="bg-gray-900 text-gray-200 border-gray-800">
					<DialogHeader>
						<DialogTitle>Delete Note</DialogTitle>
					</DialogHeader>
					<p>Are you sure you want to delete "{selectedNote?.title}"? This action cannot be undone.</p>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsDeleteDialogOpen(false)}
							className="border-gray-700 bg-gray-800 text-gray-100 hover:bg-gray-700 hover:text-white"
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={deleteNote}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			<Dialog open={isGroupManagementOpen} onOpenChange={setIsGroupManagementOpen}>
				<DialogContent className="bg-gray-900 text-gray-200 border-gray-800 max-w-md">
					<DialogHeader>
						<DialogTitle>{editingGroupId ? "Edit Group" : "Manage Groups"}</DialogTitle>
					</DialogHeader>
					{editingGroupId === null ? (
						<>
							<div className="space-y-4">
								<div className="flex items-center gap-2">
									<Input
										value={newGroupName}
										onChange={(e) => setNewGroupName(e.target.value)}
										placeholder="New group name..."
										className="bg-gray-800 border-gray-700"
									/>
									<Select value={newGroupColor} onValueChange={setNewGroupColor}>
										<SelectTrigger className="w-[80px] bg-gray-800 border-gray-700">
											<div
												className="w-5 h-5 rounded-full"
												style={{ backgroundColor: newGroupColor }}
											></div>
										</SelectTrigger>
										<SelectContent className="bg-gray-800 border-gray-700">
											{availableColors.map((color) => (
												<SelectItem
													key={color.name}
													value={color.hex}
													className="text-white hover:bg-gray-700 focus:bg-gray-700 data-[highlighted]:bg-gray-700"
												>
													<div className="flex items-center gap-2">
														<div
															className="w-4 h-4 rounded-full"
															style={{ backgroundColor: color.hex }}
														></div>
														<span className="capitalize">{color.name}</span>
													</div>
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<Button
										onClick={saveGroup}
										disabled={!newGroupName.trim()}
										size="sm"
										className="bg-teal-600 hover:bg-teal-500 text-white border-0"
									>
										<Plus className="h-4 w-4" />
									</Button>
								</div>
								<div className="space-y-2 max-h-[300px] overflow-y-auto pr-2">
									<h3 className="text-sm font-medium text-gray-400">Existing Groups</h3>
									{groups.map((group) => (
										<div
											key={group.id}
											className="flex items-center justify-between bg-gray-800/50 p-2 rounded-md"
										>
											<div className="flex items-center gap-2">
												<div
													className={`w-3 h-3 rounded-full ${
														group.color.startsWith("#") ? "" : group.color
													}`}
													style={
														group.color.startsWith("#")
															? { backgroundColor: group.color }
															: {}
													}
												></div>
												<span>{group.name}</span>
											</div>
											<div className="flex items-center gap-1">
												<Button
													variant="ghost"
													size="icon"
													className="h-7 w-7 text-gray-400 hover:text-teal-400"
													onClick={() => startEditGroup(group)}
												>
													<Edit className="h-3.5 w-3.5" />
												</Button>
												<Button
													variant="ghost"
													size="icon"
													className="h-7 w-7 text-gray-400 hover:text-red-400"
													onClick={() => deleteGroup(group.id)}
													disabled={groups.length <= 1 && group.id === "other"}
												>
													<Trash2 className="h-3.5 w-3.5" />
												</Button>
											</div>
										</div>
									))}
								</div>
							</div>
							<DialogFooter>
								<Button
									onClick={() => setIsGroupManagementOpen(false)}
									className="bg-teal-600 hover:bg-teal-500 text-white border-0"
								>
									Done
								</Button>
							</DialogFooter>
						</>
					) : (
						<>
							<div className="space-y-4">
								<div className="flex items-center gap-2">
									<Input
										value={newGroupName}
										onChange={(e) => setNewGroupName(e.target.value)}
										placeholder="Group name..."
										className="bg-gray-800 border-gray-700"
									/>
									<Select value={newGroupColor} onValueChange={setNewGroupColor}>
										<SelectTrigger className="w-[80px] bg-gray-800 border-gray-700">
											<div
												className="w-5 h-5 rounded-full"
												style={{ backgroundColor: newGroupColor }}
											></div>
										</SelectTrigger>
										<SelectContent className="bg-gray-800 border-gray-700">
											{availableColors.map((color) => (
												<SelectItem
													key={color.name}
													value={color.hex}
													className="text-white hover:bg-gray-700 focus:bg-gray-700 data-[highlighted]:bg-gray-700"
												>
													<div className="flex items-center gap-2">
														<div
															className="w-4 h-4 rounded-full"
															style={{ backgroundColor: color.hex }}
														></div>
														<span className="capitalize">{color.name}</span>
													</div>
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>
							</div>
							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => setEditingGroupId(null)}
									className="border-gray-700 bg-gray-800 text-gray-100 hover:bg-gray-700 hover:text-white"
								>
									Cancel
								</Button>
								<Button
									onClick={saveGroup}
									disabled={!newGroupName.trim()}
									className="bg-teal-600 hover:bg-teal-500 text-white border-0"
								>
									Save Changes
								</Button>
							</DialogFooter>
						</>
					)}
				</DialogContent>
			</Dialog>
		</div>
	);
}
