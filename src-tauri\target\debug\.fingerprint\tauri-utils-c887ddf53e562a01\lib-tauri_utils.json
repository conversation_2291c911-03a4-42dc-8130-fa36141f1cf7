{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 17620442681569047700, "deps": [[561782849581144631, "html5ever", false, 10557885390922558682], [1200537532907108615, "url<PERSON><PERSON>n", false, 12525775106203666172], [3150220818285335163, "url", false, 2321506650773928468], [3191507132440681679, "serde_untagged", false, 18055240464579849214], [4899080583175475170, "semver", false, 12441613648920560227], [5578504951057029730, "serde_with", false, 13828960187838659027], [5986029879202738730, "log", false, 9278501114978704911], [6262254372177975231, "kuchiki", false, 346722143937227260], [6606131838865521726, "ctor", false, 5907934880114814316], [7170110829644101142, "json_patch", false, 785519674481747452], [8319709847752024821, "uuid", false, 9030361025120995818], [9010263965687315507, "http", false, 12964141729515555854], [9451456094439810778, "regex", false, 3002757348578438955], [9689903380558560274, "serde", false, 10595498013908148355], [10806645703491011684, "thiserror", false, 13552791310023530115], [11989259058781683633, "dunce", false, 7697725579014557771], [13625485746686963219, "anyhow", false, 8405856431614235549], [14132538657330703225, "brotli", false, 15899027199303902825], [15367738274754116744, "serde_json", false, 6112304692101926396], [15609422047640926750, "toml", false, 11694222455984919893], [15622660310229662834, "walkdir", false, 15393871983861372544], [15932120279885307830, "memchr", false, 15897870240684571428], [17146114186171651583, "infer", false, 1559356671607841291], [17155886227862585100, "glob", false, 3838655522958775532], [17186037756130803222, "phf", false, 16074179348141578164]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-c887ddf53e562a01\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}