{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 11098224100583484512, "deps": [[376837177317575824, "build_script_build", false, 6729047707292466655], [4143744114649553716, "raw_window_handle", false, 8558078080454601299], [5986029879202738730, "log", false, 9278501114978704911], [10281541584571964250, "windows_sys", false, 8960047074658250694]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-3721c629e398f4a0\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}