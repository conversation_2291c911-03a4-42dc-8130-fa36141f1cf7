import { supabase } from "@/lib/supabase/client"
import type { TasksData, NotesData, RemindersConfigData, StatisticsData, AppSettingsData, StorageResult } from "./types"

type DataSection = "tasks" | "notes" | "reminders_config" | "statistics" | "app_settings"

interface OptimizedStorageOptions {
  enableOptimisticUpdates?: boolean
  batchSize?: number
  maxRetries?: number
  retryDelay?: number
  enableCompression?: boolean
}

interface ConflictResolutionStrategy {
  strategy: 'client_wins' | 'server_wins' | 'merge_by_timestamp' | 'manual'
  mergeFunction?: (local: any, remote: any) => any
}

export class OptimizedSupabaseStorage {
  private options: Required<OptimizedStorageOptions>
  private pendingOperations = new Map<string, Promise<any>>()
  private cache = new Map<string, { data: any; timestamp: number; version: number }>()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes

  constructor(options: OptimizedStorageOptions = {}) {
    this.options = {
      enableOptimisticUpdates: true,
      batchSize: 5,
      maxRetries: 3,
      retryDelay: 1000,
      enableCompression: false,
      ...options
    }
  }

  // Enhanced retry mechanism with exponential backoff
  private async withRetry<T>(
    operation: () => Promise<T>,
    context: string,
    maxRetries = this.options.maxRetries
  ): Promise<T> {
    let lastError: Error = new Error(`Operation failed after ${maxRetries} retries`)
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        console.warn(`⚠️ ${context} attempt ${attempt}/${maxRetries} failed:`, lastError.message)
        
        if (attempt < maxRetries) {
          // Exponential backoff with jitter
          const delay = this.options.retryDelay * Math.pow(2, attempt - 1) + Math.random() * 1000
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }
    
    throw lastError
  }

  // Optimistic update with rollback capability
  private async optimisticUpdate<T>(
    cacheKey: string,
    newData: T,
    remoteOperation: () => Promise<StorageResult<T>>
  ): Promise<StorageResult<T>> {
    if (!this.options.enableOptimisticUpdates) {
      return await remoteOperation()
    }

    // Store current state for rollback
    const previousState = this.cache.get(cacheKey)
    
    // Apply optimistic update
    this.cache.set(cacheKey, {
      data: newData,
      timestamp: Date.now(),
      version: (previousState?.version || 0) + 1
    })

    try {
      const result = await remoteOperation()
      
      // Update cache with server response
      if (result.success && result.data) {
        this.cache.set(cacheKey, {
          data: result.data,
          timestamp: Date.now(),
          version: (previousState?.version || 0) + 1
        })
      }
      
      return result
    } catch (error) {
      // Rollback on failure
      if (previousState) {
        this.cache.set(cacheKey, previousState)
      } else {
        this.cache.delete(cacheKey)
      }
      throw error
    }
  }

  // Conflict resolution
  private resolveConflict<T>(
    local: T & { updated_at: string; version?: number },
    remote: T & { updated_at: string; version?: number },
    strategy: ConflictResolutionStrategy
  ): T {
    switch (strategy.strategy) {
      case 'client_wins':
        return local
      case 'server_wins':
        return remote
      case 'merge_by_timestamp':
        return new Date(local.updated_at) > new Date(remote.updated_at) ? local : remote
      case 'manual':
        return strategy.mergeFunction ? strategy.mergeFunction(local, remote) : remote
      default:
        return remote
    }
  }

  // Enhanced save with conflict detection
  async saveTasks(
    userId: string, 
    data: TasksData,
    conflictStrategy: ConflictResolutionStrategy = { strategy: 'merge_by_timestamp' }
  ): Promise<StorageResult<TasksData>> {
    const cacheKey = `tasks_${userId}`
    
    return this.optimisticUpdate(cacheKey, data, async () => {
      return this.withRetry(async () => {
        console.log(`💾 Saving tasks for user ${userId}:`, { taskCount: data.tasks.length })

        // Check for conflicts first
        const { data: existing } = await supabase
          .from("user_tasks")
          .select("tasks, updated_at, version")
          .eq("user_id", userId)
          .single()

        let finalData = data
        if (existing && existing.updated_at > data.updated_at) {
          console.log(`⚠️ Conflict detected for tasks, resolving...`)
          finalData = this.resolveConflict(data, existing, conflictStrategy)
        }

        const payload = {
          user_id: userId,
          tasks: finalData.tasks,
          updated_at: new Date().toISOString(),
        }

        const { data: result, error } = await supabase
          .from("user_tasks")
          .upsert(payload, { onConflict: "user_id" })
          .select("tasks, updated_at, version")
          .single()

        if (error) {
          console.error(`❌ Save tasks error:`, error)
          await this.logSyncAttempt(userId, "tasks", "upload", "error", error.message, data.updated_at)
          throw new Error(`Supabase save tasks failed: ${error.message}`)
        }

        await this.logSyncAttempt(userId, "tasks", "upload", "success", "Tasks saved successfully", result.updated_at)
        
        return {
          success: true,
          data: { tasks: result.tasks, updated_at: result.updated_at },
          source: "supabase" as const,
        }
      }, "Save tasks")
    })
  }

  // Batch operations for multiple data sections
  async saveAll(
    userId: string,
    allData: {
      tasks?: TasksData
      notes?: NotesData
      reminders?: RemindersConfigData
      statistics?: StatisticsData
      settings?: AppSettingsData
    }
  ): Promise<{ [K in keyof typeof allData]: StorageResult<any> }> {
    const operations = []
    const results: any = {}

    if (allData.tasks) {
      operations.push(
        this.saveTasks(userId, allData.tasks).then(result => {
          results.tasks = result
        })
      )
    }

    if (allData.notes) {
      operations.push(
        this.saveNotes(userId, allData.notes).then(result => {
          results.notes = result
        })
      )
    }

    if (allData.reminders) {
      operations.push(
        this.saveRemindersConfig(userId, allData.reminders).then(result => {
          results.reminders = result
        })
      )
    }

    if (allData.statistics) {
      operations.push(
        this.saveStatistics(userId, allData.statistics).then(result => {
          results.statistics = result
        })
      )
    }

    if (allData.settings) {
      operations.push(
        this.saveAppSettings(userId, allData.settings).then(result => {
          results.settings = result
        })
      )
    }

    // Execute all operations in parallel
    await Promise.allSettled(operations)
    return results
  }

  // Enhanced load with caching
  async loadTasks(userId: string, useCache = true): Promise<StorageResult<TasksData | null>> {
    const cacheKey = `tasks_${userId}`
    
    // Check cache first
    if (useCache) {
      const cached = this.cache.get(cacheKey)
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        console.log(`📦 Loading tasks from cache for user: ${userId}`)
        return { success: true, data: cached.data, source: "supabase" }
      }
    }

    return this.withRetry(async () => {
      console.log(`📥 Loading tasks for user: ${userId}`)

      const { data: result, error } = await supabase
        .from("user_tasks")
        .select("tasks, updated_at, version")
        .eq("user_id", userId)
        .single()

      if (error) {
        if (error.code === "PGRST116") {
          console.log(`📝 No remote tasks found for user: ${userId}`)
          await this.logSyncAttempt(userId, "tasks", "download", "success", "No remote data")
          return { success: true, data: null, source: "supabase" }
        }
        console.error(`❌ Load tasks error:`, error)
        await this.logSyncAttempt(userId, "tasks", "download", "error", error.message)
        throw new Error(`Supabase load tasks failed: ${error.message}`)
      }

      const data = { tasks: result.tasks, updated_at: result.updated_at }
      
      // Update cache
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now(),
        version: result.version || 1
      })

      await this.logSyncAttempt(userId, "tasks", "download", "success", "Tasks loaded successfully", result.updated_at)
      return { success: true, data, source: "supabase" }
    }, "Load tasks")
  }

  // Placeholder methods for other data types (implement similar optimizations)
  async saveNotes(userId: string, data: NotesData): Promise<StorageResult<NotesData>> {
    // Implementation similar to saveTasks with conflict resolution
    throw new Error("Not implemented yet - use existing saveNotes for now")
  }

  async saveRemindersConfig(userId: string, data: RemindersConfigData): Promise<StorageResult<RemindersConfigData>> {
    throw new Error("Not implemented yet - use existing saveRemindersConfig for now")
  }

  async saveStatistics(userId: string, data: StatisticsData): Promise<StorageResult<StatisticsData>> {
    throw new Error("Not implemented yet - use existing saveStatistics for now")
  }

  async saveAppSettings(userId: string, data: AppSettingsData): Promise<StorageResult<AppSettingsData>> {
    throw new Error("Not implemented yet - use existing saveAppSettings for now")
  }

  // Sync logging (enhanced)
  private async logSyncAttempt(
    userId: string,
    section: DataSection,
    operation: "upload" | "download" | "merge",
    result: "success" | "error" | "partial",
    message?: string,
    dataTimestamp?: string
  ): Promise<void> {
    try {
      await supabase.from("sync_log").insert({
        user_id: userId,
        section,
        operation,
        result,
        message,
        data_timestamp: dataTimestamp,
      })
    } catch (error) {
      console.warn("Failed to log sync attempt:", error)
    }
  }

  // Cache management
  clearCache(userId?: string): void {
    if (userId) {
      // Clear cache for specific user
      for (const [key] of this.cache) {
        if (key.includes(userId)) {
          this.cache.delete(key)
        }
      }
    } else {
      // Clear all cache
      this.cache.clear()
    }
  }

  // Get cache statistics
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}
