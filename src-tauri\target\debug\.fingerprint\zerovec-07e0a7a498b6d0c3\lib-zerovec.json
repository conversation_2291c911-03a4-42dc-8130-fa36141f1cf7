{"rustc": 16591470773350601817, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 15657897354478470176, "path": 7603359693975987644, "deps": [[9620753569207166497, "zerovec_derive", false, 8023066619057294014], [10706449961930108323, "yoke", false, 13827689780801779000], [17046516144589451410, "zerofrom", false, 5559647924431264374]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-07e0a7a498b6d0c3\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}