"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Bug, RefreshCw, Shield, Database, HardDrive } from "lucide-react"
import type { User } from "@supabase/supabase-js"
import type {
  TasksData,
  NotesData,
  RemindersConfigData,
  StatisticsData,
  AppSettingsData,
  SyncStatus,
} from "@/lib/storage/types"
import { storageService } from "@/lib/storage/storage-service"

interface DebugPanelProps {
  user: User | null
  loading: boolean
  error: string | null
  tasksData: TasksData | null
  notesData: NotesData | null
  remindersConfigData: RemindersConfigData | null
  statisticsData: StatisticsData | null
  appSettingsData: AppSettingsData | null
  syncStatus: SyncStatus
  isOnline: boolean
  onLoadAllData: () => Promise<void>
  onSyncAllData: () => Promise<void>
  onClearAllData: () => Promise<void>
}

export function DebugPanel({
  user,
  loading,
  error,
  tasksData,
  notesData,
  remindersConfigData,
  statisticsData,
  appSettingsData,
  syncStatus,
  isOnline,
  onLoadAllData,
  onSyncAllData,
  onClearAllData,
}: DebugPanelProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [localData, setLocalData] = useState<any>(null)
  const [remoteData, setRemoteData] = useState<any>(null)
  const [loadingComparison, setLoadingComparison] = useState(false)

  const getDataStatus = (data: any, name: string) => {
    if (!data) return { status: "missing", color: "bg-red-500" }

    // Check if data appears to be default/empty
    const epochTime = new Date(0).toISOString()
    if (data.updated_at === epochTime) return { status: "default", color: "bg-yellow-500" }

    if (name === "tasks" && data.tasks?.length === 0) return { status: "empty", color: "bg-yellow-500" }
    if (name === "notes" && data.notes?.length === 0) return { status: "empty", color: "bg-yellow-500" }
    if (name === "reminders" && !data.text) return { status: "empty", color: "bg-yellow-500" }
    if (name === "statistics" && Object.keys(data.weeklyStats || {}).length === 0)
      return { status: "empty", color: "bg-yellow-500" }
    if (name === "appSettings" && !data.activeTab) return { status: "empty", color: "bg-yellow-500" }
    return { status: "loaded", color: "bg-green-500" }
  }

  const formatTimestamp = (timestamp: string) => {
    if (!timestamp || timestamp === new Date(0).toISOString()) return "Never"
    return new Date(timestamp).toLocaleString()
  }

  const loadDataComparison = async () => {
    if (!user) return

    setLoadingComparison(true)
    try {
      // Load local data
      const localTasks = await storageService.localStorage.loadTasks()
      const localNotes = await storageService.localStorage.loadNotes()
      const localReminders = await storageService.localStorage.loadRemindersConfig()
      const localStats = await storageService.localStorage.loadStatistics()
      const localSettings = await storageService.localStorage.loadAppSettings()

      // Load remote data
      const remoteTasks = await storageService.supabaseStorage.loadTasks(user.id)
      const remoteNotes = await storageService.supabaseStorage.loadNotes(user.id)
      const remoteReminders = await storageService.supabaseStorage.loadRemindersConfig(user.id)
      const remoteStats = await storageService.supabaseStorage.loadStatistics(user.id)
      const remoteSettings = await storageService.supabaseStorage.loadAppSettings(user.id)

      setLocalData({
        tasks: localTasks.data,
        notes: localNotes.data,
        reminders: localReminders.data,
        statistics: localStats.data,
        appSettings: localSettings.data,
      })

      setRemoteData({
        tasks: remoteTasks.data,
        notes: remoteNotes.data,
        reminders: remoteReminders.data,
        statistics: remoteStats.data,
        appSettings: remoteSettings.data,
      })
    } catch (err) {
      console.error("Failed to load data comparison:", err)
    } finally {
      setLoadingComparison(false)
    }
  }

  const compareDataSources = (localItem: any, remoteItem: any, name: string) => {
    if (!localItem && !remoteItem) return { winner: "none", reason: "No data in either source" }
    if (!localItem && remoteItem) return { winner: "remote", reason: "Only remote has data" }
    if (localItem && !remoteItem) return { winner: "local", reason: "Only local has data" }

    const epochTime = new Date(0).toISOString()
    const isLocalDefault = localItem.updated_at === epochTime
    const isRemoteDefault = remoteItem.updated_at === epochTime

    if (isLocalDefault && !isRemoteDefault) return { winner: "remote", reason: "Local is default data" }
    if (!isLocalDefault && isRemoteDefault) return { winner: "local", reason: "Remote is default data" }

    const localTime = new Date(localItem.updated_at).getTime()
    const remoteTime = new Date(remoteItem.updated_at).getTime()

    if (localTime > remoteTime) return { winner: "local", reason: "Local is newer" }
    if (remoteTime > localTime) return { winner: "remote", reason: "Remote is newer" }

    return { winner: "local", reason: "Timestamps equal, prefer local" }
  }

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="fixed bottom-4 right-4 z-50 bg-gray-800/80 hover:bg-gray-700/80 text-gray-300 hover:text-white"
        onClick={() => setIsOpen(true)}
      >
        <Bug className="h-4 w-4" />
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="bg-gray-900 text-gray-200 border-gray-800 max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Debug Panel - Data Synchronization</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Authentication Status */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Authentication Status
              </h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>Email: {user?.email || "Not signed in"}</div>
                <div>User ID: {user?.id || "None"}</div>
                <div>Loading: {loading ? "Yes" : "No"}</div>
                <div>Online: {isOnline ? "Yes" : "No"}</div>
              </div>
              {error && (
                <div className="p-2 bg-red-900/20 border border-red-800/30 rounded text-red-400 text-sm">
                  Error: {error}
                </div>
              )}
            </div>

            {/* Current Data Status */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Current Data Status</h3>
              <div className="grid grid-cols-1 gap-2">
                {[
                  { name: "tasks", data: tasksData, count: tasksData?.tasks?.length || 0 },
                  { name: "notes", data: notesData, count: notesData?.notes?.length || 0 },
                  { name: "reminders", data: remindersConfigData, count: remindersConfigData?.text?.length || 0 },
                  {
                    name: "statistics",
                    data: statisticsData,
                    count: Object.keys(statisticsData?.weeklyStats || {}).length,
                  },
                  { name: "appSettings", data: appSettingsData, count: appSettingsData ? 1 : 0 },
                ].map(({ name, data, count }) => {
                  const status = getDataStatus(data, name)
                  return (
                    <div key={name} className="flex items-center justify-between p-2 bg-gray-800/50 rounded">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${status.color}`} />
                        <span className="capitalize">{name}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-400">
                        <span>Count: {count}</span>
                        <Badge variant="outline" className="text-xs">
                          {status.status}
                        </Badge>
                        {data?.updated_at && (
                          <span className="text-xs">Updated: {formatTimestamp(data.updated_at)}</span>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Sync Status */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Sync Status</h3>
              <div className="space-y-2 text-sm">
                <div>Last Sync: {formatTimestamp(syncStatus.lastSync)}</div>
                <div>Sync In Progress: {syncStatus.syncInProgress ? "Yes" : "No"}</div>
                {syncStatus.lastError && (
                  <div className="p-2 bg-red-900/20 border border-red-800/30 rounded text-red-400">
                    Last Error: {syncStatus.lastError}
                  </div>
                )}
                <div>
                  <span>Pending Changes: </span>
                  {Object.entries(syncStatus.pendingChanges || {}).map(([key, pending]) => (
                    <Badge key={key} variant={pending ? "destructive" : "secondary"} className="ml-1 text-xs">
                      {key}: {pending ? "Yes" : "No"}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Data Source Comparison */}
            {user && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Data Source Comparison</h3>
                  <Button onClick={loadDataComparison} disabled={loadingComparison} size="sm" variant="outline">
                    {loadingComparison ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Database className="h-4 w-4 mr-2" />
                    )}
                    Compare Sources
                  </Button>
                </div>

                {(localData || remoteData) && (
                  <div className="space-y-3">
                    {["tasks", "notes", "reminders", "statistics", "appSettings"].map((section) => {
                      const local = localData?.[section]
                      const remote = remoteData?.[section]
                      const comparison = compareDataSources(local, remote, section)

                      return (
                        <div key={section} className="p-3 bg-gray-800/30 rounded border border-gray-700/50">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium capitalize">{section}</span>
                            <Badge
                              variant={
                                comparison.winner === "local"
                                  ? "default"
                                  : comparison.winner === "remote"
                                    ? "secondary"
                                    : "outline"
                              }
                              className="text-xs"
                            >
                              {comparison.winner === "local"
                                ? "Local Wins"
                                : comparison.winner === "remote"
                                  ? "Remote Wins"
                                  : "No Data"}
                            </Badge>
                          </div>
                          <div className="text-xs text-gray-400 mb-2">{comparison.reason}</div>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div className="flex items-center gap-2">
                              <HardDrive className="h-3 w-3" />
                              <span>Local: {local ? formatTimestamp(local.updated_at) : "None"}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Database className="h-3 w-3" />
                              <span>Remote: {remote ? formatTimestamp(remote.updated_at) : "None"}</span>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            )}

            {/* Raw Data Preview */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Raw Data Preview</h3>
              <div className="space-y-2">
                {tasksData && (
                  <details className="bg-gray-800/50 p-2 rounded">
                    <summary className="cursor-pointer text-sm font-medium">Tasks Data</summary>
                    <pre className="text-xs mt-2 overflow-x-auto">{JSON.stringify(tasksData, null, 2)}</pre>
                  </details>
                )}
                {notesData && (
                  <details className="bg-gray-800/50 p-2 rounded">
                    <summary className="cursor-pointer text-sm font-medium">Notes Data</summary>
                    <pre className="text-xs mt-2 overflow-x-auto">{JSON.stringify(notesData, null, 2)}</pre>
                  </details>
                )}
                {remindersConfigData && (
                  <details className="bg-gray-800/50 p-2 rounded">
                    <summary className="cursor-pointer text-sm font-medium">Reminders Data</summary>
                    <pre className="text-xs mt-2 overflow-x-auto">{JSON.stringify(remindersConfigData, null, 2)}</pre>
                  </details>
                )}
                {statisticsData && (
                  <details className="bg-gray-800/50 p-2 rounded">
                    <summary className="cursor-pointer text-sm font-medium">Statistics Data</summary>
                    <pre className="text-xs mt-2 overflow-x-auto">{JSON.stringify(statisticsData, null, 2)}</pre>
                  </details>
                )}
                {appSettingsData && (
                  <details className="bg-gray-800/50 p-2 rounded">
                    <summary className="cursor-pointer text-sm font-medium">App Settings Data</summary>
                    <pre className="text-xs mt-2 overflow-x-auto">{JSON.stringify(appSettingsData, null, 2)}</pre>
                  </details>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Actions</h3>
              <div className="flex flex-wrap gap-2">
                <Button onClick={onLoadAllData} size="sm" variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reload All Data
                </Button>
                <Button onClick={onSyncAllData} size="sm" variant="outline" disabled={!user || !isOnline}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Force Sync
                </Button>
                <Button onClick={onClearAllData} size="sm" variant="destructive">
                  Clear Local Data
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
