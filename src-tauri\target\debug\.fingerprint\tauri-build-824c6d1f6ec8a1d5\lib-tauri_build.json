{"rustc": 16591470773350601817, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 719223188098001672, "deps": [[4899080583175475170, "semver", false, 716847677502116053], [6913375703034175521, "schemars", false, 15448400005307545174], [7170110829644101142, "json_patch", false, 7182569762026392866], [9689903380558560274, "serde", false, 15667921800837325527], [11050281405049894993, "tauri_utils", false, 13399737882237405111], [12714016054753183456, "tauri_winres", false, 7201866424788321790], [13077543566650298139, "heck", false, 7051206041825333927], [13475171727366188400, "cargo_toml", false, 12560224319065534024], [13625485746686963219, "anyhow", false, 8405856431614235549], [15367738274754116744, "serde_json", false, 6579130706674131689], [15609422047640926750, "toml", false, 3957483134449868550], [15622660310229662834, "walkdir", false, 7013720488400282455], [16928111194414003569, "dirs", false, 527921675173805062], [17155886227862585100, "glob", false, 3838655522958775532]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-824c6d1f6ec8a1d5\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}