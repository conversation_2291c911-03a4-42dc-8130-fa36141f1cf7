import { supabase } from "@/lib/supabase/client";
import type {
	TasksData,
	NotesData,
	RemindersConfigData,
	StatisticsData,
	AppSettingsData,
	StorageResult,
} from "./types";

type DataSection = "tasks" | "notes" | "reminders_config" | "statistics" | "app_settings";

export class SupabaseStorageService {
	private static instance: SupabaseStorageService;
	private retryAttempts = 3;
	private retryDelay = 1000;
	private maxRetryDelay = 10000;

	static getInstance(): SupabaseStorageService {
		if (!SupabaseStorageService.instance) {
			SupabaseStorageService.instance = new SupabaseStorageService();
		}
		return SupabaseStorageService.instance;
	}

	private formatError(error: unknown): string {
		if (error && typeof error === "object" && "message" in error) {
			return String(error.message);
		}
		return "Unknown Supabase error";
	}

	private async logSyncAttempt(
		userId: string,
		section: DataSection,
		operation: string, // 'upload', 'download'
		status: "success" | "error",
		errorMessage?: string | null,
		localUpdatedAt?: string,
		remoteUpdatedAt?: string
	): Promise<void> {
		try {
			const logEntry = {
				user_id: userId,
				section,
				operation,
				status,
				error_message: errorMessage || null,
				local_updated_at: localUpdatedAt ? new Date(localUpdatedAt).toISOString() : null,
				remote_updated_at: remoteUpdatedAt ? new Date(remoteUpdatedAt).toISOString() : null,
			};

			console.log(`📝 Logging sync attempt:`, logEntry);

			const { data, error: logError } = await supabase.from("sync_log").insert(logEntry).select();

			if (logError) {
				// Don't throw errors for sync logging failures, just log them
				console.warn("⚠️ Failed to log sync attempt (non-critical):", logError.message);

				// Try to setup RLS policies if it's an RLS error
				if (logError.message.includes("row-level security policy")) {
					console.log("🔧 Attempting to setup sync_log RLS policies...");
					await this.setupSyncLogRLS();
				}
			} else {
				console.log(`✅ Sync log entry created for ${section} ${operation}`);
			}
		} catch (logError) {
			// Don't throw errors for sync logging failures, just log them
			console.warn("⚠️ Exception while logging sync attempt (non-critical):", logError);
		}
	}

	private async setupSyncLogRLS(): Promise<void> {
		try {
			const { error } = await supabase.rpc("setup_sync_log_rls");
			if (error) {
				console.warn("⚠️ Failed to setup sync_log RLS policies:", error);
			} else {
				console.log("✅ sync_log RLS policies setup successfully");
			}
		} catch (error) {
			console.warn("⚠️ Exception setting up sync_log RLS policies:", error);
		}
	}

	private async withRetry<T>(operation: () => Promise<T>): Promise<T> {
		let lastError: Error = new Error("Operation failed after multiple retries");

		for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
			try {
				const result = await operation();
				if (attempt > 1) {
					console.log(`✅ Operation succeeded on attempt ${attempt}`);
				}
				return result;
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(this.formatError(error));
				console.warn(`⚠️ Attempt ${attempt}/${this.retryAttempts} failed:`, lastError.message);

				if (attempt === this.retryAttempts) {
					console.error(`❌ Operation failed after ${this.retryAttempts} attempts:`, lastError);
					throw lastError;
				}

				const delay = Math.min(this.retryDelay * Math.pow(2, attempt - 1), this.maxRetryDelay);
				console.log(`⏳ Retrying in ${delay}ms...`);
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		throw lastError;
	}

	// --- Tasks ---
	async saveTasks(userId: string, data: TasksData): Promise<StorageResult<TasksData>> {
		return this.withRetry(async () => {
			console.log(`💾 Saving tasks for user ${userId}:`, { taskCount: data.tasks.length });

			const payload = {
				user_id: userId,
				tasks: data.tasks,
				updated_at: new Date().toISOString(),
			};

			const { data: result, error } = await supabase
				.from("user_tasks")
				.upsert(payload, { onConflict: "user_id" })
				.select("tasks, updated_at")
				.single();

			if (error) {
				console.error(`❌ Save tasks error:`, error);
				await this.logSyncAttempt(userId, "tasks", "upload", "error", error.message, data.updated_at);
				throw new Error(`Supabase save tasks failed: ${error.message}`);
			}

			console.log(`✅ Tasks saved successfully`);
			await this.logSyncAttempt(userId, "tasks", "upload", "success", null, data.updated_at, result.updated_at);
			return { success: true, data: { tasks: result.tasks, updated_at: result.updated_at }, source: "supabase" };
		});
	}

	async loadTasks(userId: string): Promise<StorageResult<TasksData | null>> {
		return this.withRetry(async () => {
			console.log(`📥 Loading tasks for user: ${userId}`);

			const { data: result, error } = await supabase
				.from("user_tasks")
				.select("tasks, updated_at")
				.eq("user_id", userId)
				.single();

			if (error) {
				if (error.code === "PGRST116") {
					// No rows found
					console.log(`📝 No remote tasks found for user: ${userId}`);
					await this.logSyncAttempt(userId, "tasks", "download", "success", "No remote data");
					return { success: true, data: null, source: "supabase" };
				}
				console.error(`❌ Load tasks error:`, error);
				await this.logSyncAttempt(userId, "tasks", "download", "error", error.message);
				throw new Error(`Supabase load tasks failed: ${error.message}`);
			}

			console.log(`✅ Tasks loaded successfully:`, { taskCount: result.tasks?.length || 0 });
			await this.logSyncAttempt(userId, "tasks", "download", "success", null, undefined, result.updated_at);
			return { success: true, data: { tasks: result.tasks, updated_at: result.updated_at }, source: "supabase" };
		});
	}

	// --- Notes ---
	async saveNotes(userId: string, data: NotesData): Promise<StorageResult<NotesData>> {
		return this.withRetry(async () => {
			console.log(`💾 Saving notes for user ${userId}:`, {
				noteCount: data.notes.length,
				groupCount: data.groups.length,
			});

			const payload = {
				user_id: userId,
				notes: data.notes,
				groups: data.groups,
				updated_at: new Date().toISOString(),
			};

			const { data: result, error } = await supabase
				.from("user_notes")
				.upsert(payload, { onConflict: "user_id" })
				.select("notes, groups, updated_at")
				.single();

			if (error) {
				console.error(`❌ Save notes error:`, error);
				await this.logSyncAttempt(userId, "notes", "upload", "error", error.message, data.updated_at);
				throw new Error(`Supabase save notes failed: ${error.message}`);
			}

			console.log(`✅ Notes saved successfully`);
			await this.logSyncAttempt(userId, "notes", "upload", "success", null, data.updated_at, result.updated_at);
			return {
				success: true,
				data: { notes: result.notes, groups: result.groups, updated_at: result.updated_at },
				source: "supabase",
			};
		});
	}

	async loadNotes(userId: string): Promise<StorageResult<NotesData | null>> {
		return this.withRetry(async () => {
			console.log(`📥 Loading notes for user: ${userId}`);

			const { data: result, error } = await supabase
				.from("user_notes")
				.select("notes, groups, updated_at")
				.eq("user_id", userId)
				.single();

			if (error) {
				if (error.code === "PGRST116") {
					console.log(`📝 No remote notes found for user: ${userId}`);
					await this.logSyncAttempt(userId, "notes", "download", "success", "No remote data");
					return { success: true, data: null, source: "supabase" };
				}
				console.error(`❌ Notes loading error:`, error);
				await this.logSyncAttempt(userId, "notes", "download", "error", error.message);
				throw new Error(`Supabase load notes failed: ${error.message}`);
			}

			console.log(`✅ Notes loaded successfully:`, {
				notesCount: result.notes?.length || 0,
				groupsCount: result.groups?.length || 0,
				updatedAt: result.updated_at,
			});

			await this.logSyncAttempt(userId, "notes", "download", "success", null, undefined, result.updated_at);
			return {
				success: true,
				data: { notes: result.notes, groups: result.groups, updated_at: result.updated_at },
				source: "supabase",
			};
		});
	}

	// --- RemindersConfig ---
	async saveRemindersConfig(userId: string, data: RemindersConfigData): Promise<StorageResult<RemindersConfigData>> {
		return this.withRetry(async () => {
			console.log(`💾 Saving reminders config for user ${userId}`);

			const payload = {
				user_id: userId,
				text: data.text,
				updated_at: new Date().toISOString(),
			};

			const { data: result, error } = await supabase
				.from("user_reminders_config")
				.upsert(payload, { onConflict: "user_id" })
				.select("text, updated_at")
				.single();

			if (error) {
				console.error(`❌ Save reminders config error:`, error);
				await this.logSyncAttempt(
					userId,
					"reminders_config",
					"upload",
					"error",
					error.message,
					data.updated_at
				);
				throw new Error(`Supabase save reminders_config failed: ${error.message}`);
			}

			console.log(`✅ Reminders config saved successfully`);
			await this.logSyncAttempt(
				userId,
				"reminders_config",
				"upload",
				"success",
				null,
				data.updated_at,
				result.updated_at
			);
			return { success: true, data: { text: result.text, updated_at: result.updated_at }, source: "supabase" };
		});
	}

	async loadRemindersConfig(userId: string): Promise<StorageResult<RemindersConfigData | null>> {
		return this.withRetry(async () => {
			console.log(`📥 Loading reminders config for user: ${userId}`);

			const { data: result, error } = await supabase
				.from("user_reminders_config")
				.select("text, updated_at")
				.eq("user_id", userId)
				.single();

			if (error) {
				if (error.code === "PGRST116") {
					console.log(`📝 No remote reminders config found for user: ${userId}`);
					await this.logSyncAttempt(userId, "reminders_config", "download", "success", "No remote data");
					return { success: true, data: null, source: "supabase" };
				}
				console.error(`❌ Load reminders config error:`, error);
				await this.logSyncAttempt(userId, "reminders_config", "download", "error", error.message);
				throw new Error(`Supabase load reminders_config failed: ${error.message}`);
			}

			console.log(`✅ Reminders config loaded successfully`);
			await this.logSyncAttempt(
				userId,
				"reminders_config",
				"download",
				"success",
				null,
				undefined,
				result.updated_at
			);
			return { success: true, data: { text: result.text, updated_at: result.updated_at }, source: "supabase" };
		});
	}

	// --- Statistics ---
	async saveStatistics(userId: string, data: StatisticsData): Promise<StorageResult<StatisticsData>> {
		return this.withRetry(async () => {
			console.log(`💾 Saving statistics for user ${userId}:`, {
				weeklyStatsKeys: Object.keys(data.weeklyStats).length,
				streak: data.streak,
			});

			const payload = {
				user_id: userId,
				weekly_stats: data.weeklyStats,
				streak: data.streak,
				updated_at: new Date().toISOString(),
			};

			const { data: result, error } = await supabase
				.from("user_statistics")
				.upsert(payload, { onConflict: "user_id" })
				.select("weekly_stats, streak, updated_at")
				.single();

			if (error) {
				console.error(`❌ Save statistics error:`, error);
				await this.logSyncAttempt(userId, "statistics", "upload", "error", error.message, data.updated_at);
				throw new Error(`Supabase save statistics failed: ${error.message}`);
			}

			console.log(`✅ Statistics saved successfully`);
			await this.logSyncAttempt(
				userId,
				"statistics",
				"upload",
				"success",
				null,
				data.updated_at,
				result.updated_at
			);
			return {
				success: true,
				data: { weeklyStats: result.weekly_stats, streak: result.streak, updated_at: result.updated_at },
				source: "supabase",
			};
		});
	}

	async loadStatistics(userId: string): Promise<StorageResult<StatisticsData | null>> {
		return this.withRetry(async () => {
			console.log(`📥 Loading statistics for user: ${userId}`);

			const { data: result, error } = await supabase
				.from("user_statistics")
				.select("weekly_stats, streak, updated_at")
				.eq("user_id", userId)
				.single();

			if (error) {
				if (error.code === "PGRST116") {
					console.log(`📝 No remote statistics found for user: ${userId}`);
					await this.logSyncAttempt(userId, "statistics", "download", "success", "No remote data");
					return { success: true, data: null, source: "supabase" };
				}
				console.error(`❌ Load statistics error:`, error);
				await this.logSyncAttempt(userId, "statistics", "download", "error", error.message);
				throw new Error(`Supabase load statistics failed: ${error.message}`);
			}

			console.log(`✅ Statistics loaded successfully:`, {
				weeklyStatsKeys: Object.keys(result.weekly_stats || {}).length,
				streak: result.streak,
			});
			await this.logSyncAttempt(userId, "statistics", "download", "success", null, undefined, result.updated_at);
			return {
				success: true,
				data: { weeklyStats: result.weekly_stats, streak: result.streak, updated_at: result.updated_at },
				source: "supabase",
			};
		});
	}

	// --- AppSettings ---
	async saveAppSettings(userId: string, data: AppSettingsData): Promise<StorageResult<AppSettingsData>> {
		return this.withRetry(async () => {
			console.log(`💾 Saving app settings for user ${userId}:`, {
				activeTab: data.activeTab,
				currentQuoteId: data.currentQuoteId,
				lastCompletionDay: data.lastCompletionDay,
				lastSelectedNoteId: data.lastSelectedNoteId,
			});

			const payload = {
				user_id: userId,
				active_tab: data.activeTab,
				current_quote_id: data.currentQuoteId,
				current_quote_date: data.currentQuoteDate,
				last_completion_day: data.lastCompletionDay,
				last_selected_note_id: data.lastSelectedNoteId,
				updated_at: new Date().toISOString(),
			};

			const { data: result, error } = await supabase
				.from("user_app_settings")
				.upsert(payload, { onConflict: "user_id" })
				.select(
					"active_tab, current_quote_id, current_quote_date, last_completion_day, last_selected_note_id, updated_at"
				)
				.single();

			if (error) {
				console.error(`❌ Save app settings error:`, error);
				await this.logSyncAttempt(userId, "app_settings", "upload", "error", error.message, data.updated_at);
				throw new Error(`Supabase save app_settings failed: ${error.message}`);
			}

			console.log(`✅ App settings saved successfully`);
			await this.logSyncAttempt(
				userId,
				"app_settings",
				"upload",
				"success",
				null,
				data.updated_at,
				result.updated_at
			);
			return {
				success: true,
				data: {
					activeTab: result.active_tab,
					currentQuoteId: result.current_quote_id,
					currentQuoteDate: result.current_quote_date,
					lastCompletionDay: result.last_completion_day,
					lastSelectedNoteId: result.last_selected_note_id,
					updated_at: result.updated_at,
				},
				source: "supabase",
			};
		});
	}

	async loadAppSettings(userId: string): Promise<StorageResult<AppSettingsData | null>> {
		return this.withRetry(async () => {
			console.log(`📥 Loading app settings for user: ${userId}`);

			const { data: result, error } = await supabase
				.from("user_app_settings")
				.select("active_tab, current_quote_id, current_quote_date, last_completion_day, last_selected_note_id, updated_at")
				.eq("user_id", userId)
				.single();

			if (error) {
				if (error.code === "PGRST116") {
					console.log(`📝 No remote app settings found for user: ${userId}`);
					await this.logSyncAttempt(userId, "app_settings", "download", "success", "No remote data");
					return { success: true, data: null, source: "supabase" };
				}
				console.error(`❌ Load app settings error:`, error);
				await this.logSyncAttempt(userId, "app_settings", "download", "error", error.message);
				throw new Error(`Supabase load app_settings failed: ${error.message}`);
			}

			console.log(`✅ App settings loaded successfully:`, {
				activeTab: result.active_tab,
				lastCompletionDay: result.last_completion_day,
			});
			await this.logSyncAttempt(
				userId,
				"app_settings",
				"download",
				"success",
				null,
				undefined,
				result.updated_at
			);
			return {
				success: true,
				data: {
					activeTab: result.active_tab,
					currentQuoteId: result.current_quote_id,
					currentQuoteDate: result.current_quote_date,
					lastCompletionDay: result.last_completion_day,
					lastSelectedNoteId: result.last_selected_note_id,
					updated_at: result.updated_at,
				},
				source: "supabase",
			};
		});
	}

	async checkConnection(): Promise<boolean> {
		try {
			// A light query to check connectivity, e.g., fetching a single row or a specific small table.
			// Using a limit query on a potentially non-existent user_id is fine for a health check.
			const { error } = await supabase.from("user_app_settings").select("user_id").limit(1);
			// If error is PGRST116 (no rows found), it's still a successful connection.
			// Any other error might indicate a connection issue.
			return !error || error.code === "PGRST116";
		} catch {
			return false;
		}
	}
}
