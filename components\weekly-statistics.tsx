"use client"
import { cn } from "@/lib/utils"
import { statisticsService } from "@/lib/statistics-service"
import type { Task } from "@/lib/storage/types"

interface WeeklyStatisticsProps {
  weeklyData: Record<string, Record<string, number>> // date -> category -> points
  tasks?: Task[]
}

const CATEGORIES = [
  { key: "sport", label: "SPORT", color: "bg-cyan-500", dotColor: "bg-cyan-400" },
  {
    key: "self-improvement",
    label: "SELF-IMPROVEMENT",
    color: "bg-purple-500",
    dotColor: "bg-purple-400",
  },
  { key: "work", label: "WORK", color: "bg-emerald-500", dotColor: "bg-emerald-400" },
  { key: "stretching", label: "STRETCHING", color: "bg-yellow-500", dotColor: "bg-yellow-400" },
  { key: "reading", label: "READING", color: "bg-pink-500", dotColor: "bg-pink-400" },
]

export function WeeklyStatistics({ weeklyData, tasks = [] }: WeeklyStatisticsProps) {
  const maxValue = 100 // Percentage is out of 100

  // Calculate maximum possible points
  const maxPossiblePoints = statisticsService.calculateMaxPossiblePoints(tasks)

  // Calculate percentages for display
  const percentages = statisticsService.calculatePercentages(weeklyData, maxPossiblePoints)

  // Get date range string for the last 7 days
  const dateRangeString = statisticsService.getLast7DaysRangeString()

  // Only show categories that have tasks defined or have data (excluding uncategorized)
  const categoriesToShow = CATEGORIES.filter(
    (category) => maxPossiblePoints[category.key] > 0 || percentages[category.key] > 0,
  )

  return (
    <div className="space-y-3 bg-gray-900/0 p-1 rounded-lg">
      <div className="px-1 pt-1 text-xs font-medium text-gray-400 tracking-wider">{dateRangeString} (LAST 7 DAYS)</div>

      {categoriesToShow.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p className="text-sm">No categorized tasks found</p>
          <p className="text-xs mt-1">Add categories and points to tasks to see statistics</p>
        </div>
      ) : (
        <div className="space-y-1.5">
          {categoriesToShow.map((category) => {
            const value = percentages[category.key] || 0
            const percentage = Math.min(Math.max(value, 0), 100) // Clamp between 0 and 100
            const maxPoints = maxPossiblePoints[category.key] || 0

            return (
              <div
                key={category.key}
                className="group relative h-7 bg-gray-800/60 overflow-hidden border border-gray-700/40 rounded-md shadow-sm"
                title={`${category.label}: ${percentage}% (Max possible: ${maxPoints} points/week)`}
              >
                {/* Subtle grid pattern for the background of the bar */}
                <div
                  className="absolute inset-0 opacity-[0.03]"
                  style={{
                    backgroundImage:
                      "linear-gradient(45deg, #fff 25%, transparent 25%), linear-gradient(-45deg, #fff 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #fff 75%), linear-gradient(-45deg, transparent 75%, #fff 75%)",
                    backgroundSize: "6px 6px",
                    backgroundPosition: "0 0, 0 3px, 3px -3px, -3px 0px",
                  }}
                />

                {/* Progress fill */}
                {percentage > 0 && (
                  <div
                    className={cn("absolute inset-y-0 left-0 transition-all duration-700 ease-out", category.color)}
                    style={{
                      width: `${percentage}%`,
                    }}
                  >
                    {/* Inner subtle pattern for the fill */}
                    <div
                      className="absolute inset-0 opacity-[0.15]"
                      style={{
                        backgroundImage:
                          "repeating-linear-gradient(-45deg, transparent, transparent 4px, rgba(255,255,255,0.3) 4px, rgba(255,255,255,0.3) 8px)",
                        backgroundSize: "12px 12px", // Adjusted for a slightly larger pattern
                      }}
                    />
                  </div>
                )}

                {/* Content: Dot, Label, Percentage */}
                <div className="relative h-full flex items-center justify-between px-3 z-10">
                  <div className="flex items-center gap-2">
                    <div className={cn("w-2 h-2 rounded-full", category.dotColor)} />
                    <span className="text-xs font-medium text-gray-200 tracking-wide">{category.label}</span>
                  </div>
                  <span className="text-xs font-medium text-gray-300">{percentage}%</span>
                </div>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}
