"use client"

import type React from "react"

import { useState, useRef, useCallback, useMemo } from "react"

interface SwipeConfig {
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  minSwipeDistance?: number
  maxSwipeTime?: number
  threshold?: number
}

interface SwipeState {
  isActive: boolean
  direction: "left" | "right" | null
  progress: number
  velocity: number
}

export function useOptimizedSwipe(config: SwipeConfig) {
  const { onSwipeLeft, onSwipeRight, minSwipeDistance = 60, maxSwipeTime = 400, threshold = 0.3 } = config

  const [swipeState, setSwipeState] = useState<SwipeState>({
    isActive: false,
    direction: null,
    progress: 0,
    velocity: 0,
  })

  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null)
  const rafRef = useRef<number>()

  const updateSwipeState = useCallback((updates: Partial<SwipeState>) => {
    setSwipeState((prev) => ({ ...prev, ...updates }))
  }, [])

  const resetSwipeState = useCallback(() => {
    setSwipeState({
      isActive: false,
      direction: null,
      progress: 0,
      velocity: 0,
    })
  }, [])

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0]
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now(),
    }
  }, [])

  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (!touchStartRef.current) return

      const touch = e.touches[0]
      const deltaX = touch.clientX - touchStartRef.current.x
      const deltaY = touch.clientY - touchStartRef.current.y
      const deltaTime = Date.now() - touchStartRef.current.time

      // Only handle horizontal swipes
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
        e.preventDefault()

        const direction = deltaX > 0 ? "right" : "left"
        const progress = Math.min(Math.abs(deltaX) / minSwipeDistance, 1)
        const velocity = Math.abs(deltaX) / Math.max(deltaTime, 1)

        // Use RAF for smooth updates
        if (rafRef.current) {
          cancelAnimationFrame(rafRef.current)
        }

        rafRef.current = requestAnimationFrame(() => {
          updateSwipeState({
            isActive: true,
            direction,
            progress,
            velocity,
          })
        })
      }
    },
    [minSwipeDistance, updateSwipeState],
  )

  const handleTouchEnd = useCallback(() => {
    if (!touchStartRef.current) {
      resetSwipeState()
      return
    }

    const { progress, direction, velocity } = swipeState
    const shouldTrigger = progress >= threshold || velocity > 0.5

    if (shouldTrigger && direction) {
      if (direction === "left" && onSwipeLeft) {
        onSwipeLeft()
      } else if (direction === "right" && onSwipeRight) {
        onSwipeRight()
      }
    }

    // Reset with animation
    setTimeout(resetSwipeState, 150)
    touchStartRef.current = null

    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current)
    }
  }, [swipeState, threshold, onSwipeLeft, onSwipeRight, resetSwipeState])

  const swipeHandlers = useMemo(
    () => ({
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd,
    }),
    [handleTouchStart, handleTouchMove, handleTouchEnd],
  )

  return {
    swipeState,
    swipeHandlers,
  }
}
