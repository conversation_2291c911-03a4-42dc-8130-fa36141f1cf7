"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { Check, Edit, Plus, Save, Trash2 } from "lucide-react";
import type React from "react";
import { useEffect, useRef, useState } from "react";

interface Task {
	time: string;
	label: string;
	completed: boolean;
	points?: number;
	category?: string;
	duration?: number;
}

interface TimelineProps {
	tasks: Task[];
	setTasks: React.Dispatch<React.SetStateAction<Task[]>>;
	isLocked?: boolean;
}

const CATEGORIES = [
	{ value: "uncategorized", label: "Uncategorized" },
	{ value: "sport", label: "Sport" },
	{ value: "self-improvement", label: "Self-Improvement" },
	{ value: "work", label: "Work" },
	{ value: "stretching", label: "Stretching" },
	{ value: "reading", label: "Reading" },
];

const DEFAULT_TASK_DURATION = 30;

export function Timeline({ tasks: taskList, setTasks: setTaskList, isLocked = false }: TimelineProps) {
	const [isEditMode, setIsEditMode] = useState(false);
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [currentTask, setCurrentTask] = useState<Task | null>(null);
	const [currentIndex, setCurrentIndex] = useState<number | null>(null);
	const [newTaskTime, setNewTaskTime] = useState("");
	const [newTaskLabel, setNewTaskLabel] = useState("");
	const [newTaskPoints, setNewTaskPoints] = useState("");
	const [newTaskCategory, setNewTaskCategory] = useState("");

	const [recentlyChecked, setRecentlyChecked] = useState<number | null>(null);
	const recentlyCheckedTimeoutRef = useRef<NodeJS.Timeout>();

	const [progressBarStyle, setProgressBarStyle] = useState({
		top: "0px",
		height: "0px",
		progress: 0,
	});

	const containerRef = useRef<HTMLDivElement>(null);
	const taskListRef = useRef<HTMLUListElement>(null);
	const taskRefs = useRef<(HTMLLIElement | null)[]>([]);

	const timeToMinutes = (timeStr: string): number => {
		const [hours, minutes] = timeStr.split(":").map(Number);
		return hours * 60 + minutes;
	};

	const sortedTasks = [...taskList].sort((a, b) => {
		const timeA = timeToMinutes(a.time);
		const timeB = timeToMinutes(b.time);
		return timeA - timeB;
	});

	const calculateProgress = (): { progress: number; nextTaskIndex: number } => {
		if (sortedTasks.length === 0) {
			return { progress: 0, nextTaskIndex: -1 };
		}

		const now = new Date();
		const currentMinutes = now.getHours() * 60 + now.getMinutes();

		let nextTaskIndex = -1;
		for (let i = 0; i < sortedTasks.length; i++) {
			const taskTime = timeToMinutes(sortedTasks[i].time);
			if (taskTime > currentMinutes) {
				nextTaskIndex = i;
				break;
			}
		}

		if (nextTaskIndex === -1) {
			return { progress: 100, nextTaskIndex: -1 };
		}

		if (nextTaskIndex === 0) {
			const firstTaskTime = timeToMinutes(sortedTasks[0].time);
			const progressToFirstTask = (currentMinutes / firstTaskTime) * (1 / sortedTasks.length);
			return {
				progress: Math.min(progressToFirstTask * 100, (1 / sortedTasks.length) * 100),
				nextTaskIndex: 0,
			};
		}

		const prevTaskTime = timeToMinutes(sortedTasks[nextTaskIndex - 1].time);
		const nextTaskTime = timeToMinutes(sortedTasks[nextTaskIndex].time);

		const timeBetweenTasks = nextTaskTime - prevTaskTime;
		const timePassedSincePrevTask = currentMinutes - prevTaskTime;

		const progressBetweenTasks = timePassedSincePrevTask / timeBetweenTasks;
		const baseProgress = (nextTaskIndex / sortedTasks.length) * 100;
		const additionalProgress = (progressBetweenTasks / sortedTasks.length) * 100;

		return {
			progress: Math.min(baseProgress + additionalProgress, 100),
			nextTaskIndex,
		};
	};

	const { progress, nextTaskIndex } = calculateProgress();

	useEffect(() => {
		if (sortedTasks.length === 0 || !containerRef.current || !taskListRef.current) {
			setProgressBarStyle({ top: "0px", height: "0px", progress: 0 });
			return;
		}

		const updateProgressBarPosition = () => {
			const firstTask = taskRefs.current[0];
			const lastTask = taskRefs.current[sortedTasks.length - 1];

			if (!firstTask || !lastTask || !containerRef.current) return;

			const containerRect = containerRef.current.getBoundingClientRect();
			const firstTaskRect = firstTask.getBoundingClientRect();
			const lastTaskRect = lastTask.getBoundingClientRect();

			// Align with the actual top and bottom of the tasks
			const topOffset = firstTaskRect.top - containerRect.top;
			const totalHeight = lastTaskRect.bottom - firstTaskRect.top;

			setProgressBarStyle({
				top: `${topOffset}px`,
				height: `${totalHeight}px`,
				progress,
			});
		};

		setTimeout(updateProgressBarPosition, 0);
		window.addEventListener("resize", updateProgressBarPosition);
		return () => window.removeEventListener("resize", updateProgressBarPosition);
	}, [sortedTasks.length, progress]);

	useEffect(() => {
		const interval = setInterval(() => {
			const { progress: newProgress } = calculateProgress();
			setProgressBarStyle((prev) => ({ ...prev, progress: newProgress }));
		}, 60000);

		return () => clearInterval(interval);
	}, [sortedTasks]);

	const isTaskTimePassed = (taskTime: string): boolean => {
		const now = new Date();
		const currentMinutes = now.getHours() * 60 + now.getMinutes();
		const taskMinutes = timeToMinutes(taskTime);
		return taskMinutes < currentMinutes;
	};

	const toggleTask = (index: number) => {
		// Don't allow toggling if locked
		if (isLocked) return;

		if (recentlyCheckedTimeoutRef.current) {
			clearTimeout(recentlyCheckedTimeoutRef.current);
		}
		const updatedTasks = [...taskList];
		const taskToUpdate = updatedTasks.find(
			(task) => task.time === sortedTasks[index].time && task.label === sortedTasks[index].label
		);
		if (taskToUpdate) {
			taskToUpdate.completed = !taskToUpdate.completed;
			setTaskList(updatedTasks);
			setRecentlyChecked(index);
			recentlyCheckedTimeoutRef.current = setTimeout(() => {
				setRecentlyChecked(null);
			}, 500);
		}
	};

	const toggleEditMode = () => setIsEditMode(!isEditMode);

	const openAddDialog = () => {
		setNewTaskTime("");
		setNewTaskLabel("");
		setNewTaskPoints("");
		setNewTaskCategory("");
		setIsAddDialogOpen(true);
	};

	const openEditDialog = (task: Task, index: number) => {
		setCurrentTask(task);
		setCurrentIndex(taskList.findIndex((t) => t.time === task.time && t.label === task.label));
		setNewTaskTime(task.time);
		setNewTaskLabel(task.label);
		setNewTaskPoints(task.points?.toString() || "");
		setNewTaskCategory(task.category || "");
		setIsEditDialogOpen(true);
	};

	const openDeleteDialog = (task: Task, index: number) => {
		setCurrentTask(task);
		setCurrentIndex(taskList.findIndex((t) => t.time === task.time && t.label === task.label));
		setIsDeleteDialogOpen(true);
	};

	const addTask = () => {
		if (!newTaskTime || !newTaskLabel) return;
		const newTask: Task = {
			time: newTaskTime,
			label: newTaskLabel,
			completed: false,
			points: newTaskPoints ? Number.parseInt(newTaskPoints) : undefined,
			category: newTaskCategory || undefined,
		};
		const updatedTasks = [...taskList, newTask].sort((a, b) => {
			const timeA = timeToMinutes(a.time);
			const timeB = timeToMinutes(b.time);
			return timeA - timeB;
		});
		setTaskList(updatedTasks);
		setIsAddDialogOpen(false);
	};

	const editTask = () => {
		if (currentIndex === null || !newTaskTime || !newTaskLabel) return;
		const updatedTasks = [...taskList];
		updatedTasks[currentIndex] = {
			...updatedTasks[currentIndex],
			time: newTaskTime,
			label: newTaskLabel,
			points: newTaskPoints ? Number.parseInt(newTaskPoints) : undefined,
			category: newTaskCategory || undefined,
		};
		const sortedTasks = updatedTasks.sort((a, b) => {
			const timeA = timeToMinutes(a.time);
			const timeB = timeToMinutes(b.time);
			return timeA - timeB;
		});
		setTaskList(sortedTasks);
		setIsEditDialogOpen(false);
	};

	const deleteTask = () => {
		if (currentIndex === null) return;
		const updatedTasks = taskList.filter((_, index) => index !== currentIndex);
		setTaskList(updatedTasks);
		setIsDeleteDialogOpen(false);
	};

	useEffect(() => {
		taskRefs.current = taskRefs.current.slice(0, sortedTasks.length);
	}, [sortedTasks.length]);

	useEffect(() => {
		return () => {
			if (recentlyCheckedTimeoutRef.current) {
				clearTimeout(recentlyCheckedTimeoutRef.current);
			}
		};
	}, []);

	const getCurrentTimeInfo = () => {
		const now = new Date();
		const currentTime = `${now.getHours().toString().padStart(2, "0")}:${now
			.getMinutes()
			.toString()
			.padStart(2, "0")}`;

		return {
			currentTime,
			progress: progress.toFixed(1),
			nextTask: nextTaskIndex >= 0 ? sortedTasks[nextTaskIndex] : null,
		};
	};

	const timeInfo = getCurrentTimeInfo();

	return (
		<div className="h-full flex flex-col overflow-hidden">
			<div className="flex items-center justify-between mb-5 flex-shrink-0">
				<div className="flex flex-col">
					<h2 className="text-xl font-semibold text-white">Daily Timeline</h2>
				</div>
				<div className="flex gap-2">
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							"text-gray-400 hover:text-teal-400 hover:bg-gray-800/50",
							isEditMode && "text-teal-400 bg-gray-800/50"
						)}
						onClick={toggleEditMode}
					>
						{isEditMode ? (
							<>
								<Save className="h-4 w-4 mr-1.5" />
								Done
							</>
						) : (
							<>
								<Edit className="h-4 w-4 mr-1.5" />
								Edit
							</>
						)}
					</Button>
				</div>
			</div>

			<div ref={containerRef} className="relative flex-1 min-h-0 overflow-hidden">
				{/* Progress Bar */}
				<div className="absolute left-0 top-0 bottom-0 w-6 flex items-stretch z-10">
					<div className="relative w-full flex justify-center">
						{/* Background bar */}
						<div
							className="absolute w-3 bg-gray-800/60 overflow-hidden border border-gray-700/40 rounded-[4px] shadow-sm"
							style={{
								top: progressBarStyle.top,
								height: progressBarStyle.height,
							}}
						>
							{/* Subtle grid pattern for the background */}
							<div
								className="absolute inset-0 opacity-[0.03]"
								style={{
									backgroundImage:
										"linear-gradient(45deg, #fff 25%, transparent 25%), linear-gradient(-45deg, #fff 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #fff 75%), linear-gradient(-45deg, transparent 75%, #fff 75%)",
									backgroundSize: "6px 6px",
									backgroundPosition: "0 0, 0 3px, 3px -3px, -3px 0px",
								}}
							/>
						</div>

						{/* Progress fill - aligned with checkpoint positions */}
						{progressBarStyle.progress > 0 && (
							<div
								className="absolute w-3 bg-teal-500 transition-all duration-1000 ease-out overflow-hidden rounded-[4px]"
								style={{
									top: progressBarStyle.top,
									height: (() => {
										if (sortedTasks.length === 0) return "0px";

										// Calculate the exact fill height based on current progress and task positions
										const currentProgress = progressBarStyle.progress / 100;

										if (currentProgress >= 1) {
											// If we're past all tasks, fill to the last task's checkpoint
											const lastTaskRef = taskRefs.current[sortedTasks.length - 1];
											if (lastTaskRef && containerRef.current) {
												const containerRect = containerRef.current.getBoundingClientRect();
												const lastTaskRect = lastTaskRef.getBoundingClientRect();
												const lastTaskMidpoint =
													lastTaskRect.top - containerRect.top + lastTaskRect.height / 2;
												return `${
													lastTaskMidpoint - Number.parseFloat(progressBarStyle.top)
												}px`;
											}
											return progressBarStyle.height;
										}

										// Find which task segment we're in
										const taskIndex = Math.floor(currentProgress * sortedTasks.length);
										const segmentProgress = (currentProgress * sortedTasks.length) % 1;

										if (taskIndex >= sortedTasks.length) {
											// Past all tasks
											const lastTaskRef = taskRefs.current[sortedTasks.length - 1];
											if (lastTaskRef && containerRef.current) {
												const containerRect = containerRef.current.getBoundingClientRect();
												const lastTaskRect = lastTaskRef.getBoundingClientRect();
												const lastTaskMidpoint =
													lastTaskRect.top - containerRect.top + lastTaskRect.height / 2;
												return `${
													lastTaskMidpoint - Number.parseFloat(progressBarStyle.top)
												}px`;
											}
											return progressBarStyle.height;
										}

										const currentTaskRef = taskRefs.current[taskIndex];
										if (!currentTaskRef || !containerRef.current) {
											return `${currentProgress * Number.parseFloat(progressBarStyle.height)}px`;
										}

										const containerRect = containerRef.current.getBoundingClientRect();
										const currentTaskRect = currentTaskRef.getBoundingClientRect();
										const currentTaskMidpoint =
											currentTaskRect.top - containerRect.top + currentTaskRect.height / 2;

										if (taskIndex === 0) {
											// First task segment - fill from start to current task's midpoint based on progress
											return `${currentTaskMidpoint - Number.parseFloat(progressBarStyle.top)}px`;
										}

										// Between tasks - interpolate between previous and current task midpoints
										const prevTaskRef = taskRefs.current[taskIndex - 1];
										if (prevTaskRef) {
											const prevTaskRect = prevTaskRef.getBoundingClientRect();
											const prevTaskMidpoint =
												prevTaskRect.top - containerRect.top + prevTaskRect.height / 2;
											const interpolatedHeight =
												prevTaskMidpoint +
												segmentProgress * (currentTaskMidpoint - prevTaskMidpoint);
											return `${interpolatedHeight - Number.parseFloat(progressBarStyle.top)}px`;
										}

										return `${currentTaskMidpoint - Number.parseFloat(progressBarStyle.top)}px`;
									})(),
								}}
							>
								{/* Inner subtle pattern for the fill */}
								<div
									className="absolute inset-0 opacity-[0.15]"
									style={{
										backgroundImage:
											"repeating-linear-gradient(-45deg, transparent, transparent 4px, rgba(255,255,255,0.3) 4px, rgba(255,255,255,0.3) 8px)",
										backgroundSize: "12px 12px",
									}}
								/>
							</div>
						)}

						{/* Task checkpoint dots */}
						{sortedTasks.map((task, index) => {
							const taskRef = taskRefs.current[index];
							if (!taskRef || !containerRef.current) return null;

							const containerRect = containerRef.current.getBoundingClientRect();
							const taskRect = taskRef.getBoundingClientRect();
							const taskMidpoint = taskRect.top - containerRect.top + taskRect.height / 2;

							return (
								<div
									key={`${task.time}-${task.label}`}
									className={cn(
										"absolute w-1.5 h-1.5 rounded-full border-2 border-gray-900 transition-all duration-300 z-20 shadow-md",
										isLocked
											? "bg-gray-500 border-gray-400 shadow-gray-500/30" // Grey when locked
											: task.completed
											? "bg-teal-400 border-teal-300 shadow-teal-400/50"
											: isTaskTimePassed(task.time)
											? "bg-red-400 border-red-300 shadow-red-400/50"
											: "bg-gray-400 border-gray-300 shadow-gray-400/30"
									)}
									style={{
										top: `${taskMidpoint - 3}px`, // Center the 6px dot vertically
										left: "50%",
										transform: "translateX(-50%)", // Center horizontally on the progress bar
									}}
								>
									{/* Inner glow effect for completed tasks (but not when locked) */}
									{task.completed && !isLocked && (
										<div className="absolute inset-0 rounded-full bg-teal-300/30 animate-pulse" />
									)}
								</div>
							);
						})}
					</div>
				</div>

				{/* Task List - Scrollable area */}
				<div className="h-full flex flex-col pl-8 overflow-y-auto overflow-x-hidden scrollbar-none">
					{sortedTasks.length > 0 ? (
						<ul ref={taskListRef} className="space-y-3 py-1">
							{sortedTasks.map((task, index) => {
								const isPastTime = isTaskTimePassed(task.time);
								const isOverdue = isPastTime && !task.completed;

								return (
									<li
										key={`${task.time}-${task.label}`}
										ref={(el) => (taskRefs.current[index] = el)}
										className={cn(
											"group relative",
											recentlyChecked === index && task.completed && "z-10",
											isEditMode && "py-1"
										)}
									>
										<div className="flex items-start relative">
											<div
												onClick={() => toggleTask(index)}
												className={cn(
													"flex items-center bg-gray-800/50 backdrop-blur-sm rounded-lg border w-full overflow-hidden transition-all duration-300 relative cursor-pointer h-14",
													isLocked
														? "border-gray-700/30 opacity-60 cursor-not-allowed" // Grey border when locked
														: task.completed
														? "border-teal-800/30"
														: isOverdue
														? "border-red-800/50"
														: "border-gray-700/30",
													recentlyChecked === index &&
														task.completed &&
														!isLocked &&
														"task-complete-animation border-teal-500/50"
												)}
												role="button"
												tabIndex={isLocked ? -1 : 0} // Disable tab focus when locked
												onKeyDown={(e) => {
													if (isLocked) return; // Skip keyboard handling when locked
													if (e.key === "Enter" || e.key === " ") {
														e.preventDefault(); // Prevent default scrolling behavior
														toggleTask(index);
													}
												}}
												onMouseDown={(e) => {
													e.preventDefault(); // Prevent focus-related scrolling
												}}
												aria-pressed={task.completed}
												aria-disabled={isLocked} // Add ARIA attribute for accessibility
												aria-label={`Task: ${task.label}, Time: ${task.time}. Status: ${
													task.completed ? "Completed" : "Pending"
												}${isLocked ? ". Locked until tomorrow" : ""}`}
											>
												{recentlyChecked === index && task.completed && !isLocked && (
													<div className="absolute inset-0 z-0 pointer-events-none">
														<div className="ripple-effect"></div>
													</div>
												)}
												{recentlyChecked === index && task.completed && !isLocked && (
													<div className="absolute inset-0 bg-gradient-to-r from-teal-500/20 to-transparent slide-effect z-0"></div>
												)}

												<div
													className={cn(
														"text-sm font-medium py-3 px-4 bg-gray-800/70 w-16 shrink-0 border-r border-gray-700/30 relative z-10 h-full flex items-center",
														isLocked
															? "text-gray-500" // Grey when locked
															: task.completed
															? "text-gray-500"
															: isOverdue
															? "text-red-400"
															: "text-gray-300"
													)}
												>
													{task.time}
												</div>
												<div
													className={cn(
														"flex-1 p-3 transition-all duration-300 relative z-10 h-full flex items-center",
														isLocked
															? "opacity-60" // Grey when locked
															: task.completed
															? "opacity-60"
															: "opacity-100",
														!isLocked && isOverdue && "bg-red-900/10", // Only show red background when not locked
														recentlyChecked === index &&
															task.completed &&
															!isLocked &&
															"bg-teal-900/10"
													)}
												>
													<div className="flex items-center gap-3 w-full">
														<div
															className={cn(
																"w-5 h-5 border rounded flex items-center justify-center shrink-0 transition-all duration-200",
																isLocked
																	? "border-gray-500 bg-gray-700/50" // Grey when locked
																	: task.completed
																	? "bg-teal-600 border-teal-600"
																	: isOverdue
																	? "border-red-500"
																	: "border-gray-500",
																recentlyChecked === index && task.completed && !isLocked
																	? "checkbox-pop"
																	: ""
															)}
															aria-hidden="true"
														>
															{task.completed && (
																<Check
																	className={cn(
																		"w-3.5 h-3.5",
																		isLocked ? "text-gray-400" : "text-white" // Grey checkmark when locked
																	)}
																	strokeWidth={3}
																/>
															)}
														</div>
														<div className="flex-1">
															<span
																className={cn(
																	"text-sm font-medium transition-all duration-300 block",
																	isLocked
																		? "text-gray-500" // Grey when locked
																		: task.completed
																		? "line-through text-gray-500"
																		: isOverdue
																		? "text-red-300"
																		: "text-gray-200",
																	recentlyChecked === index &&
																		task.completed &&
																		!isLocked &&
																		"text-glow text-teal-300"
																)}
															>
																{task.label}
															</span>
														</div>

														{isEditMode && (
															<div className="flex gap-1">
																<Button
																	variant="ghost"
																	size="icon"
																	className="h-7 w-7 rounded-md text-gray-400 hover:text-teal-400 hover:bg-gray-700/50"
																	onClick={(e) => {
																		e.stopPropagation();
																		openEditDialog(task, index);
																	}}
																>
																	<Edit className="h-3.5 w-3.5" />
																	<span className="sr-only">Edit Task</span>
																</Button>
																<Button
																	variant="ghost"
																	size="icon"
																	className="h-7 w-7 rounded-md text-gray-400 hover:text-red-400 hover:bg-gray-700/50"
																	onClick={(e) => {
																		e.stopPropagation();
																		openDeleteDialog(task, index);
																	}}
																>
																	<Trash2 className="h-3.5 w-3.5" />
																	<span className="sr-only">Delete Task</span>
																</Button>
															</div>
														)}
													</div>
												</div>
											</div>
										</div>
									</li>
								);
							})}
						</ul>
					) : (
						<div className="flex-1 flex items-center justify-center text-gray-500">No tasks scheduled</div>
					)}

					{isEditMode && (
						<div className="mt-4 mb-2 flex-shrink-0">
							<Button
								variant="outline"
								size="sm"
								className="w-full border-dashed border-gray-700 text-gray-400 hover:text-teal-400 hover:border-teal-500 bg-gray-800/30 hover:bg-gray-800/50"
								onClick={openAddDialog}
							>
								<Plus className="h-4 w-4 mr-1.5" />
								Add Task
							</Button>
						</div>
					)}
				</div>
			</div>

			<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
				<DialogContent className="bg-gray-900 text-gray-200 border-gray-800">
					<DialogHeader>
						<DialogTitle>Add New Task</DialogTitle>
					</DialogHeader>
					<div className="grid gap-4 py-2">
						<div className="grid gap-2">
							<Label htmlFor="time">Time</Label>
							<Input
								id="time"
								type="time"
								value={newTaskTime}
								onChange={(e) => setNewTaskTime(e.target.value)}
								className="bg-gray-800 border-gray-700"
							/>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="task">Task</Label>
							<Input
								id="task"
								placeholder="Enter task description"
								value={newTaskLabel}
								onChange={(e) => setNewTaskLabel(e.target.value)}
								className="bg-gray-800 border-gray-700"
							/>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="points">Points (optional)</Label>
							<Input
								id="points"
								type="number"
								placeholder="Enter points (1-100)"
								value={newTaskPoints}
								onChange={(e) => setNewTaskPoints(e.target.value)}
								className="bg-gray-800 border-gray-700"
								min="1"
								max="100"
							/>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="category">Category (optional)</Label>
							<Select value={newTaskCategory} onValueChange={setNewTaskCategory}>
								<SelectTrigger className="bg-gray-800 border-gray-700">
									<SelectValue placeholder="Select a category" />
								</SelectTrigger>
								<SelectContent className="bg-gray-800 border-gray-700">
									{CATEGORIES.map((category) => (
										<SelectItem
											key={category.value}
											value={category.value}
											className="text-gray-200 focus:text-white focus:bg-gray-700"
										>
											{category.label}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsAddDialogOpen(false)}
							className="border-gray-700 bg-gray-800 text-gray-100 hover:bg-gray-700 hover:text-white"
						>
							Cancel
						</Button>
						<Button onClick={addTask} className="bg-teal-600 hover:bg-teal-500 text-white border-0">
							Add Task
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
				<DialogContent className="bg-gray-900 text-gray-200 border-gray-800">
					<DialogHeader>
						<DialogTitle>Edit Task</DialogTitle>
					</DialogHeader>
					<div className="grid gap-4 py-2">
						<div className="grid gap-2">
							<Label htmlFor="edit-time">Time</Label>
							<Input
								id="edit-time"
								type="time"
								value={newTaskTime}
								onChange={(e) => setNewTaskTime(e.target.value)}
								className="bg-gray-800 border-gray-700"
							/>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="edit-task">Task</Label>
							<Input
								id="edit-task"
								placeholder="Enter task description"
								value={newTaskLabel}
								onChange={(e) => setNewTaskLabel(e.target.value)}
								className="bg-gray-800 border-gray-700"
							/>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="edit-points">Points (optional)</Label>
							<Input
								id="edit-points"
								type="number"
								placeholder="Enter points (1-100)"
								value={newTaskPoints}
								onChange={(e) => setNewTaskPoints(e.target.value)}
								className="bg-gray-800 border-gray-700"
								min="1"
								max="100"
							/>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="edit-category">Category (optional)</Label>
							<Select value={newTaskCategory} onValueChange={setNewTaskCategory}>
								<SelectTrigger className="bg-gray-800 border-gray-700">
									<SelectValue placeholder="Select a category" />
								</SelectTrigger>
								<SelectContent className="bg-gray-800 border-gray-700">
									{CATEGORIES.map((category) => (
										<SelectItem
											key={category.value}
											value={category.value}
											className="text-gray-200 focus:text-white focus:bg-gray-700"
										>
											{category.label}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsEditDialogOpen(false)}
							className="border-gray-700 bg-gray-800 text-gray-100 hover:bg-gray-700 hover:text-white"
						>
							Cancel
						</Button>
						<Button onClick={editTask} className="bg-teal-600 hover:bg-teal-500 text-white border-0">
							Save Changes
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			<Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
				<DialogContent className="bg-gray-900 text-gray-200 border-gray-800">
					<DialogHeader>
						<DialogTitle>Delete Task</DialogTitle>
					</DialogHeader>
					<p>
						Are you sure you want to delete the task "{currentTask?.label}" at {currentTask?.time}? This
						action cannot be undone.
					</p>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsDeleteDialogOpen(false)}
							className="border-gray-700 bg-gray-800 text-gray-100 hover:bg-gray-700 hover:text-white"
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={deleteTask}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
