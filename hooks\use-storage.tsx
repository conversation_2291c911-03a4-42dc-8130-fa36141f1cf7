"use client";

import { storageService } from "@/lib/storage/storage-service";
import type {
	AllAppData,
	AppSettingsData,
	NotesData,
	RemindersConfigData,
	StatisticsData,
	SyncStatus,
	TasksData,
} from "@/lib/storage/types";
import { supabase } from "@/lib/supabase/client";
import type { User } from "@supabase/supabase-js";
import { useCallback, useEffect, useRef, useState } from "react";

interface UseStorageReturn {
	tasksData: TasksData | null;
	notesData: NotesData | null;
	remindersConfigData: RemindersConfigData | null;
	statisticsData: StatisticsData | null;
	appSettingsData: AppSettingsData | null;
	user: User | null;
	loading: boolean;
	savingSection: Partial<Record<keyof AllAppData, boolean>>; // Track saving per section
	error: string | null;
	syncStatus: SyncStatus;
	isOnline: boolean;
	saveTasks: (data: TasksData) => Promise<boolean>;
	saveNotes: (data: NotesData) => Promise<boolean>;
	saveRemindersConfig: (data: RemindersConfigData) => Promise<boolean>;
	saveStatistics: (data: StatisticsData) => Promise<boolean>;
	saveAppSettings: (data: AppSettingsData) => Promise<boolean>;
	loadAllData: () => Promise<void>;
	syncAllData: () => Promise<void>;
	clearAllLocalData: () => Promise<void>;
	signIn: (email: string, password: string) => Promise<boolean>;
	signUp: (email: string, password: string) => Promise<boolean>;
	signOut: () => Promise<boolean>;
	compareAndUseRecentData: () => Promise<void>;
	resetLoadingState: () => void;
}

// Reduced loading timeout for faster UX
const LOADING_TIMEOUT = 5000; // 5 seconds instead of 10

// Development auto-login credentials - only attempt if explicitly enabled
const DEV_AUTO_LOGIN = {
	email: "<EMAIL>",
	password: "TP08@vAbsMc42p1pN#7H",
	enabled: true, // Disabled by default to speed up loading
};

export function useStorage(): UseStorageReturn {
	const [tasksData, setTasksData] = useState<TasksData | null>(null);
	const [notesData, setNotesData] = useState<NotesData | null>(null);
	const [remindersConfigData, setRemindersConfigData] = useState<RemindersConfigData | null>(null);
	const [statisticsData, setStatisticsData] = useState<StatisticsData | null>(null);
	const [appSettingsData, setAppSettingsData] = useState<AppSettingsData | null>(null);

	const [user, setUser] = useState<User | null>(null);
	const [loading, setLoading] = useState(true);
	const [savingSection, setSavingSection] = useState<Partial<Record<keyof AllAppData, boolean>>>({});
	const [error, setError] = useState<string | null>(null);

	const [syncStatus, setSyncStatus] = useState<SyncStatus>(storageService.getSyncStatus());
	const [isOnline, setIsOnline] = useState(storageService.getConnectionStatus());

	const userRef = useRef<User | null>(null);
	const hasInitializedRef = useRef(false);
	const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const autoLoginAttemptedRef = useRef(false);
	userRef.current = user;

	// Function to reset loading state if it gets stuck
	const resetLoadingState = useCallback(() => {
		console.log("🚨 Manually resetting loading state");
		setLoading(false);
		if (loadingTimeoutRef.current) {
			clearTimeout(loadingTimeoutRef.current);
			loadingTimeoutRef.current = null;
		}
	}, []);

	// Helper function to set loading with timeout safety
	const setLoadingWithTimeout = useCallback((isLoading: boolean) => {
		if (isLoading) {
			setLoading(true);
			// Clear any existing timeout
			if (loadingTimeoutRef.current) {
				clearTimeout(loadingTimeoutRef.current);
			}
			// Set new timeout to force loading to false after LOADING_TIMEOUT
			loadingTimeoutRef.current = setTimeout(() => {
				console.warn(`⚠️ Loading timeout reached (${LOADING_TIMEOUT}ms). Forcing loading state to false.`);
				setLoading(false);
				loadingTimeoutRef.current = null;
			}, LOADING_TIMEOUT);
		} else {
			setLoading(false);
			// Clear timeout when loading is explicitly set to false
			if (loadingTimeoutRef.current) {
				clearTimeout(loadingTimeoutRef.current);
				loadingTimeoutRef.current = null;
			}
		}
	}, []);

	// Optimized auto-login function - only runs if enabled and needed
	const attemptAutoLogin = useCallback(async (): Promise<User | null> => {
		if (!DEV_AUTO_LOGIN.enabled || autoLoginAttemptedRef.current) return null;
		autoLoginAttemptedRef.current = true;

		console.log("🔐 Attempting auto-login for development...");
		try {
			const { data, error: signInError } = await supabase.auth.signInWithPassword({
				email: DEV_AUTO_LOGIN.email,
				password: DEV_AUTO_LOGIN.password,
			});

			if (signInError) {
				console.warn("⚠️ Auto-login failed:", signInError.message);
				return null;
			} else {
				console.log("✅ Auto-login successful for development");
				return data.user;
			}
		} catch (err) {
			console.warn("⚠️ Auto-login exception:", err);
			return null;
		}
	}, []);

	const updateAllDataStates = useCallback((allData: AllAppData) => {
		console.log(`🔄 Updating all data states:`, {
			tasksCount: allData.tasksData?.tasks?.length || 0,
			notesCount: allData.notesData?.notes?.length || 0,
			remindersTextLength: allData.remindersConfigData?.text?.length || 0,
			weeklyStatsKeys: Object.keys(allData.statisticsData?.weeklyStats || {}),
			appSettingsActiveTab: allData.appSettingsData?.activeTab,
		});

		// Batch state updates to reduce re-renders
		setTasksData(allData.tasksData || null);
		setNotesData(allData.notesData || null);
		setRemindersConfigData(allData.remindersConfigData || null);
		setStatisticsData(allData.statisticsData || null);
		setAppSettingsData(allData.appSettingsData || null);
	}, []);

	// Memoize the handleSave function to prevent recreation on every render
	const handleSave = useCallback(
		async <TData, TKey extends keyof AllAppData>(
			sectionKey: TKey,
			data: TData,
			saveMethod: (dataToSave: TData, userId: string | null) => Promise<any>,
			updateStateMethod: (newData: TData | null) => void
		): Promise<boolean> => {
			console.log(`💾 handleSave called for ${sectionKey}`);
			setSavingSection((prev) => ({ ...prev, [sectionKey]: true }));
			setError(null);
			try {
				const result = await saveMethod(data, userRef.current?.id || null);
				if (result.success) {
					updateStateMethod(result.data || data);
					setSyncStatus(storageService.getSyncStatus());
					return true;
				} else {
					const errorMsg = result.error || `Failed to save ${sectionKey}`;
					console.error(`❌ Save failed for ${sectionKey}:`, errorMsg);
					setError(errorMsg);
					return false;
				}
			} catch (err) {
				const errorMsg = err instanceof Error ? err.message : `Unknown error saving ${sectionKey}`;
				console.error(`❌ Save exception for ${sectionKey}:`, err);
				setError(errorMsg);
				return false;
			} finally {
				setSavingSection((prev) => ({ ...prev, [sectionKey]: false }));
			}
		},
		[]
	);

	// Memoize all save functions to prevent recreation
	const saveTasks = useCallback(
		(data: TasksData) => handleSave("tasksData", data, storageService.saveTasks.bind(storageService), setTasksData),
		[handleSave]
	);

	const saveNotes = useCallback(
		(data: NotesData) => handleSave("notesData", data, storageService.saveNotes.bind(storageService), setNotesData),
		[handleSave]
	);

	const saveRemindersConfig = useCallback(
		(data: RemindersConfigData) =>
			handleSave(
				"remindersConfigData",
				data,
				storageService.saveRemindersConfig.bind(storageService),
				setRemindersConfigData
			),
		[handleSave]
	);

	const saveStatistics = useCallback(
		(data: StatisticsData) =>
			handleSave("statisticsData", data, storageService.saveStatistics.bind(storageService), setStatisticsData),
		[handleSave]
	);

	const saveAppSettings = useCallback(
		(data: AppSettingsData) =>
			handleSave(
				"appSettingsData",
				data,
				storageService.saveAppSettings.bind(storageService),
				setAppSettingsData
			),
		[handleSave]
	);

	// Optimized initialization - faster and more efficient
	useEffect(() => {
		const initAuthAndLoad = async () => {
			console.log(`🚀 Fast initialization starting...`);

			// Debug environment variables
			if (typeof window !== "undefined" && (window as any).electronDebug) {
				(window as any).electronDebug.log("Environment check:", {
					hasElectronEnv: !!(window as any).electronEnv,
					hasProcess: !!process?.env,
					supabaseUrl: process?.env?.NEXT_PUBLIC_SUPABASE_URL || "MISSING",
					userAgent: navigator.userAgent,
				});
			}

			const startTime = Date.now();
			setLoadingWithTimeout(true);

			try {
				// Check if we're running in Electron
				const isElectron = typeof window !== "undefined" && window.navigator.userAgent.includes("Electron");
				console.log(`🖥️ Running in Electron: ${isElectron}`);

				// Start loading local data immediately (don't wait for auth)
				const localDataPromise = storageService.loadAll(null);

				// Get current user in parallel (skip if in Electron)
				const userPromise = isElectron
					? Promise.resolve({ data: { user: null }, error: null })
					: supabase.auth.getUser();

				// Wait for both operations
				const [
					localData,
					{
						data: { user: currentUser },
						error: userError,
					},
				] = await Promise.all([localDataPromise, userPromise]);

				// Only throw error if it's not about missing session
				if (userError && userError.message !== "Auth session missing!") {
					throw new Error(`Auth error: ${userError.message}`);
				}

				console.log(`👤 Current user:`, currentUser?.email || "No user (not signed in)");

				// Set user state immediately
				setUser(currentUser);
				userRef.current = currentUser;
				storageService.setUserAuthentication(currentUser?.id || null, !!currentUser);

				// If we have a user, try to load their remote data in the background
				if (currentUser) {
					// Load local data first for immediate UI
					updateAllDataStates(localData);

					// Then load remote data in background and merge if needed
					storageService
						.loadAll(currentUser.id)
						.then((remoteData) => {
							updateAllDataStates(remoteData);
						})
						.catch((err) => {
							console.warn("Background remote data load failed:", err);
						});
				} else {
					// No user - just use local data
					updateAllDataStates(localData);

					// Only attempt auto-login if enabled and no user found
					if (DEV_AUTO_LOGIN.enabled) {
						attemptAutoLogin()
							.then((autoUser) => {
								if (autoUser) {
									setUser(autoUser);
									userRef.current = autoUser;
									storageService.setUserAuthentication(autoUser.id, true);
									// Load data for auto-logged user in background
									storageService.loadAll(autoUser.id).then(updateAllDataStates).catch(console.warn);
								}
							})
							.catch(console.warn);
					}
				}

				hasInitializedRef.current = true;
				const loadTime = Date.now() - startTime;
				console.log(`✅ Fast initialization completed in ${loadTime}ms`);
			} catch (err) {
				console.error("❌ Initialization error:", err);
				setError(err instanceof Error ? err.message : "Initialization failed");

				// Fallback to local data
				try {
					storageService.setUserAuthentication(null, false);
					const localData = await storageService.loadAll(null);
					updateAllDataStates(localData);
					hasInitializedRef.current = true;
				} catch (localErr) {
					console.error("❌ Failed to load local data as fallback:", localErr);
				}
			} finally {
				setLoadingWithTimeout(false);
			}
		};

		initAuthAndLoad();

		return () => {
			if (loadingTimeoutRef.current) {
				clearTimeout(loadingTimeoutRef.current);
			}
		};
	}, [updateAllDataStates, setLoadingWithTimeout, attemptAutoLogin]);

	// Optimized auth state change handler
	useEffect(() => {
		const {
			data: { subscription },
		} = supabase.auth.onAuthStateChange(async (event, session) => {
			console.log(`🔐 Auth state changed:`, event, session?.user?.email || "No user");

			// Don't reload data during initial setup
			if (!hasInitializedRef.current) {
				console.log(`⏭️ Skipping auth state change during initialization`);
				return;
			}

			// For sign out, clear immediately
			if (event === "SIGNED_OUT") {
				setUser(null);
				userRef.current = null;
				storageService.setUserAuthentication(null, false);

				// Clear and load defaults quickly
				storageService
					.clearAllLocalData()
					.then(() => {
						return storageService.loadAll(null);
					})
					.then(updateAllDataStates)
					.catch(console.error);
				return;
			}

			// For sign in events, update user and load data
			const newUser = session?.user ?? null;
			setUser(newUser);
			userRef.current = newUser;
			storageService.setUserAuthentication(newUser?.id || null, !!newUser);

			if (newUser) {
				// Load data for new user
				storageService
					.loadAll(newUser.id)
					.then(updateAllDataStates)
					.catch((err) => {
						console.error(`❌ Error loading data for new user:`, err);
						setError(`Failed to load user data: ${err instanceof Error ? err.message : String(err)}`);
					});
			}
		});

		return () => subscription.unsubscribe();
	}, [updateAllDataStates]);

	// Optimized sync status updates - less frequent
	useEffect(() => {
		const interval = setInterval(() => {
			setSyncStatus(storageService.getSyncStatus());
			setIsOnline(storageService.getConnectionStatus());
		}, 10000); // Check every 10 seconds instead of 5
		return () => clearInterval(interval);
	}, []);

	const loadAllData = useCallback(async () => {
		console.log(`🔄 Manual load all data triggered`);
		setLoadingWithTimeout(true);
		setError(null);
		try {
			const loadedData = await storageService.loadAll(userRef.current?.id || null);
			updateAllDataStates(loadedData);
		} catch (err) {
			console.error("❌ Manual load all data failed:", err);
			setError(err instanceof Error ? err.message : "Failed to load data");
		} finally {
			setLoadingWithTimeout(false);
		}
	}, [updateAllDataStates, setLoadingWithTimeout]);

	const syncAllData = useCallback(async () => {
		if (!userRef.current) {
			const errorMsg = "Cannot sync: User not signed in.";
			console.warn(`⚠️ ${errorMsg}`);
			setError(errorMsg);
			return;
		}
		if (!isOnline) {
			const errorMsg = "Cannot sync: Offline.";
			console.warn(`⚠️ ${errorMsg}`);
			setError(errorMsg);
			return;
		}
		console.log(`🔄 Manual sync all data triggered`);
		setLoadingWithTimeout(true);
		setError(null);
		try {
			const syncedData = await storageService.syncAll(userRef.current.id);
			updateAllDataStates(syncedData);
			setSyncStatus(storageService.getSyncStatus());
		} catch (err) {
			console.error("❌ Manual sync all data failed:", err);
			setError(err instanceof Error ? err.message : "Sync failed");
		} finally {
			setLoadingWithTimeout(false);
		}
	}, [isOnline, updateAllDataStates, setLoadingWithTimeout]);

	const clearAllLocalData = useCallback(async () => {
		console.log(`🗑️ Manual clear all local data triggered`);
		setLoadingWithTimeout(true);
		await storageService.clearAllLocalData();
		const defaultData = await storageService.loadAll(null);
		updateAllDataStates(defaultData);
		setSyncStatus(storageService.getSyncStatus());
		setLoadingWithTimeout(false);
	}, [updateAllDataStates, setLoadingWithTimeout]);

	const signIn = useCallback(
		async (email: string, password: string): Promise<boolean> => {
			console.log(`🔐 Sign in attempt for:`, email);
			setError(null);
			setLoadingWithTimeout(true);
			try {
				const { data, error: signInError } = await supabase.auth.signInWithPassword({ email, password });
				if (signInError) {
					console.error(`❌ Sign in failed:`, signInError.message);
					setError(signInError.message);
					return false;
				}
				console.log(`✅ Sign in successful`);
				return true;
			} catch (err) {
				console.error(`❌ Sign in exception:`, err);
				setError(`Sign in error: ${err instanceof Error ? err.message : String(err)}`);
				return false;
			} finally {
				setLoadingWithTimeout(false);
			}
		},
		[setLoadingWithTimeout]
	);

	const signUp = useCallback(
		async (email: string, password: string): Promise<boolean> => {
			console.log(`📝 Sign up attempt for:`, email);
			setError(null);
			setLoadingWithTimeout(true);
			try {
				const { data, error: signUpError } = await supabase.auth.signUp({ email, password });
				if (signUpError) {
					console.error(`❌ Sign up failed:`, signUpError.message);
					setError(signUpError.message);
					return false;
				}
				console.log(`✅ Sign up successful`);
				return true;
			} catch (err) {
				console.error(`❌ Sign up exception:`, err);
				setError(`Sign up error: ${err instanceof Error ? err.message : String(err)}`);
				return false;
			} finally {
				setLoadingWithTimeout(false);
			}
		},
		[setLoadingWithTimeout]
	);

	const signOut = useCallback(async (): Promise<boolean> => {
		console.log(`🚪 Sign out attempt`);
		setError(null);
		setLoadingWithTimeout(true);
		try {
			const { error: signOutError } = await supabase.auth.signOut();
			if (signOutError) {
				console.error(`❌ Sign out failed:`, signOutError.message);
				setError(signOutError.message);
				return false;
			}
			console.log(`✅ Sign out successful`);
			autoLoginAttemptedRef.current = false;
			return true;
		} catch (err) {
			console.error(`❌ Sign out exception:`, err);
			setError(`Sign out error: ${err instanceof Error ? err.message : String(err)}`);
			return false;
		} finally {
			setLoadingWithTimeout(false);
		}
	}, [setLoadingWithTimeout]);

	// Simplified data comparison function
	const compareAndUseRecentData = useCallback(async () => {
		if (!user) return;

		setLoadingWithTimeout(true);
		setError(null);

		try {
			console.log("🔍 Comparing local and remote data timestamps...");
			await syncAllData();
		} catch (err) {
			console.error("❌ Error comparing data timestamps:", err);
			setError(err instanceof Error ? err.message : "Failed to compare data timestamps");
		} finally {
			setLoadingWithTimeout(false);
		}
	}, [user, syncAllData, setLoadingWithTimeout]);

	return {
		tasksData,
		notesData,
		remindersConfigData,
		statisticsData,
		appSettingsData,
		user,
		loading,
		savingSection,
		error,
		syncStatus,
		isOnline,
		saveTasks,
		saveNotes,
		saveRemindersConfig,
		saveStatistics,
		saveAppSettings,
		loadAllData,
		syncAllData,
		clearAllLocalData,
		signIn,
		signUp,
		signOut,
		compareAndUseRecentData,
		resetLoadingState,
	};
}
