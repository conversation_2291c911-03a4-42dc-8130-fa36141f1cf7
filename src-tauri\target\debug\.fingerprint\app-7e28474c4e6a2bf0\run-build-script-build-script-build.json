{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 10513057046879328466], [10755362358622467486, "build_script_build", false, 4188308480585524706], [6343145987997818134, "build_script_build", false, 6759785169988125135]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-7e28474c4e6a2bf0\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}