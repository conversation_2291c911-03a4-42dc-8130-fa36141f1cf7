@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 170 80% 40%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 170 80% 40%;
  --radius: 0.75rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 170 80% 40%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 170 80% 40%;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Hide scrollbars completely */
.scrollbar-none {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.scrollbar-none::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Custom scrollbar */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
  border-radius: 20px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

/* For Firefox */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
}

/* Consistent scrollbar styling for notes components */
.notes-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.notes-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.notes-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
  border-radius: 20px;
}

.notes-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

/* For Firefox */
.notes-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
}

/* Enhanced Task completion animations */

/* Task complete animation - more dramatic slide and transform */
.task-complete-animation {
  animation: task-complete-slide 0.4s cubic-bezier(0.22, 1, 0.36, 1); /* Reduced from 0.6s to 0.4s */
}

@keyframes task-complete-slide {
  0% {
    transform: translateX(0);
  }
  20% {
    transform: translateX(8px);
  }
  60% {
    transform: translateX(-4px);
  }
  80% {
    transform: translateX(2px);
  }
  100% {
    transform: translateX(0);
  }
}

/* Task uncomplete animation */
.task-uncomplete-animation {
  animation: task-uncomplete-shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

@keyframes task-uncomplete-shake {
  0%,
  100% {
    transform: translateX(0);
  }
  20% {
    transform: translateX(-2px) rotate(-1deg);
  }
  40% {
    transform: translateX(2px) rotate(1deg);
  }
  60% {
    transform: translateX(-2px) rotate(0deg);
  }
  80% {
    transform: translateX(2px) rotate(0deg);
  }
}

/* Checkbox pop animation */
.checkbox-pop {
  animation: checkbox-pop 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes checkbox-pop {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

/* Text glow effect */
.text-glow {
  text-shadow: 0 0 8px rgba(45, 212, 191, 0.7);
  animation: text-glow-pulse 1.5s ease-in-out;
}

@keyframes text-glow-pulse {
  0% {
    text-shadow: 0 0 4px rgba(45, 212, 191, 0.3);
  }
  50% {
    text-shadow: 0 0 12px rgba(45, 212, 191, 0.8), 0 0 20px rgba(45, 212, 191, 0.4);
  }
  100% {
    text-shadow: 0 0 4px rgba(45, 212, 191, 0.3);
  }
}

/* Ripple effect */
.ripple-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(45, 212, 191, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 0.5s ease-out; /* Reduced from 0.8s to 0.5s */
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  100% {
    width: 500px;
    height: 500px;
    opacity: 0;
  }
}

/* Slide effect */
.slide-effect {
  animation: slide-effect 0.5s ease-out; /* Reduced from 0.8s to 0.5s */
}

@keyframes slide-effect {
  0% {
    transform: translateX(-100%);
    opacity: 0.7;
  }
  100% {
    transform: translateX(0);
    opacity: 0;
  }
}

/* Modify the checkmark animation to be shorter and more concise */
.checkmark-draw {
  opacity: 1;
  animation: checkmark-fade 0.5s ease-out forwards; /* Reduced from 0.8s to 0.5s */
}

.checkmark-path {
  stroke-dasharray: 30;
  stroke-dashoffset: 30;
  animation: checkmark-draw 0.2s ease-in-out forwards; /* Reduced from 0.3s to 0.2s */
}

@keyframes checkmark-draw {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes checkmark-fade {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  20% {
    opacity: 1;
    transform: scale(1.1);
  }
  80% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(1.2);
  }
}

/* Confetti animation */
.confetti-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.confetti-container::before,
.confetti-container::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  opacity: 0;
}

.confetti-container::before {
  top: 10%;
  left: 15%;
  background: #14b8a6;
  border-radius: 50%;
  animation: confetti-1 0.8s ease-out;
}

.confetti-container::after {
  top: 20%;
  left: 70%;
  background: #0ea5e9;
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
  animation: confetti-2 0.8s ease-out 0.1s;
}

.confetti-container {
  animation: confetti-container 1s ease-out;
}

@keyframes confetti-container {
  0% {
    background: radial-gradient(circle at center, rgba(45, 212, 191, 0.2) 0%, transparent 20%);
  }
  50% {
    background: radial-gradient(circle at center, rgba(45, 212, 191, 0.2) 0%, transparent 40%);
  }
  100% {
    background: radial-gradient(circle at center, rgba(45, 212, 191, 0) 0%, transparent 50%);
  }
}

@keyframes confetti-1 {
  0% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translate(-20px, 40px) rotate(180deg);
    opacity: 0;
  }
}

@keyframes confetti-2 {
  0% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translate(30px, 30px) rotate(-180deg);
    opacity: 0;
  }
}

/* Add more confetti particles with pseudo-elements */
.confetti-container::before,
.confetti-container::after,
.confetti-container {
  position: relative;
}

.confetti-container::before {
  content: "";
  position: absolute;
  width: 8px;
  height: 8px;
  top: 20%;
  left: 30%;
  background: #f59e0b;
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  animation: confetti-3 1s ease-out;
}

.confetti-container::after {
  content: "";
  position: absolute;
  width: 6px;
  height: 6px;
  top: 40%;
  left: 60%;
  background: #ec4899;
  clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
  animation: confetti-4 1s ease-out 0.2s;
}

@keyframes confetti-3 {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(-40px, 60px) rotate(360deg) scale(1);
    opacity: 0;
  }
}

@keyframes confetti-4 {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(50px, 50px) rotate(-360deg) scale(1);
    opacity: 0;
  }
}

/* Add uncheck animation */
.task-uncheck-animation {
  animation: task-uncheck-slide 0.3s cubic-bezier(0.22, 1, 0.36, 1); /* Reduced from 0.4s to 0.3s */
}

@keyframes task-uncheck-slide {
  0% {
    transform: translateX(0);
  }
  40% {
    transform: translateX(-8px);
  }
  80% {
    transform: translateX(4px);
  }
  100% {
    transform: translateX(0);
  }
}
