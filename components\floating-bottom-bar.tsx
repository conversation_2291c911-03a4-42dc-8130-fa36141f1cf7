"use client";

import { useEffect, useState } from "react";

export function FloatingBottomBar() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if we're on mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 767);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Only show on mobile
  if (!isMobile) {
    return null;
  }

  return (
    <div className="floating-bottom-bar">
      {/* This acts as a background for Android navigation controls */}
    </div>
  );
}
