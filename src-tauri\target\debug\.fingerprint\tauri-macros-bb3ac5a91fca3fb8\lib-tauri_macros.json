{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 11715448618919057518, "deps": [[3060637413840920116, "proc_macro2", false, 3443190160544583166], [7341521034400937459, "tauri_codegen", false, 12268575452922293186], [10640660562325816595, "syn", false, 13521442368071639308], [11050281405049894993, "tauri_utils", false, 13399737882237405111], [13077543566650298139, "heck", false, 7051206041825333927], [17990358020177143287, "quote", false, 16216200831819806011]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-bb3ac5a91fca3fb8\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}