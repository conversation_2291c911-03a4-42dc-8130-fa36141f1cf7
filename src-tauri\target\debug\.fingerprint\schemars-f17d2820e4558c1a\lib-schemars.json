{"rustc": 16591470773350601817, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 16270006159105356133, "deps": [[3150220818285335163, "url", false, 6091892177881750386], [6913375703034175521, "build_script_build", false, 10392293199218985722], [8319709847752024821, "uuid1", false, 3745050113768389076], [9122563107207267705, "dyn_clone", false, 9144312778817798296], [9689903380558560274, "serde", false, 15667921800837325527], [14923790796823607459, "indexmap", false, 1205636194950770915], [15367738274754116744, "serde_json", false, 6579130706674131689], [16071897500792579091, "schemars_derive", false, 4320915223109645275]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-f17d2820e4558c1a\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}