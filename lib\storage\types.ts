// Individual Data Segment Types

export interface Task {
	id: string; // Unique ID for the task, can be a UUID
	time: string;
	label: string;
	completed: boolean;
	points?: number;
	category?: string;
	createdAt: string; // ISO date string
	updatedAt: string; // ISO date string for individual task updates
}

export interface TasksData {
	tasks: Task[];
	updated_at: string; // ISO date string for when the tasks collection was last updated
}

export interface Note {
	id: string; // Unique ID for the note
	title: string;
	content: string;
	group: string; // Group ID
	createdAt: string; // ISO date string
	updatedAt: string; // ISO date string for individual note updates
}

export interface Group {
	id: string; // Unique ID for the group
	name: string;
	color: string;
	createdAt: string; // ISO date string
	updatedAt: string; // ISO date string
}

export interface NotesData {
	notes: Note[];
	groups: Group[];
	updated_at: string; // ISO date string for when the notes collection was last updated
}

export interface Quote {
	id: number;
	jp?: string;
	en?: string;
	text?: string; // for quotes from northen-star-quotes
	author?: string; // for quotes from northen-star-quotes
	translatedText?: string; // for quotes from northen-star-quotes
}

export interface RemindersConfigData {
	text: string; // The main reminders text block
	updated_at: string; // ISO date string
}

export interface StatisticsData {
	// New structure: date -> category -> points
	weeklyStats: Record<string, Record<string, number>>; // date -> { category -> points }
	streak: number;
	updated_at: string; // ISO date string
}

// Daily statistics entry for a specific date
export interface DailyStats {
	date: string; // YYYY-MM-DD format
	categoryPoints: Record<string, number>; // category -> points earned that day
	totalPoints: number;
}

export interface AppSettingsData {
	activeTab: string;
	currentQuoteId?: number;
	currentQuoteDate?: string; // Stored in YYYY-MM-DD format
	lastCompletionDay?: string; // YYYY-MM-DD format - when tasks were last completed
	lastSelectedNoteId?: string; // ID of the last selected note
	updated_at: string; // ISO date string
}

// Combined structure for convenience if needed, but services will handle them separately
export interface AllAppData {
	tasksData?: TasksData;
	notesData?: NotesData;
	remindersConfigData?: RemindersConfigData;
	statisticsData?: StatisticsData;
	appSettingsData?: AppSettingsData;
}

// For storage operations
export interface StorageResult<T = any> {
	success: boolean;
	data?: T;
	error?: string;
	source: "localStorage" | "supabase" | "merged";
}

export interface SyncStatus {
	lastSync: string; // ISO date string of the last successful full sync
	pendingChanges: {
		// Track pending changes per section
		tasks?: boolean;
		notes?: boolean;
		reminders?: boolean;
		statistics?: boolean;
		settings?: boolean;
	};
	syncInProgress: boolean;
	lastError?: string;
}

// User preferences that might not be part of AppSettingsData if they are device-specific
// For now, assuming all settings are synced.
export interface UserPreferences {
	autoSave: boolean; // This will be removed as per new requirements
	syncInterval: number; // This will be removed
	backupRetention: number; // Potentially a server-side concept
}
