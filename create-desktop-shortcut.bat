@echo off
echo Creating desktop shortcut for Northen Star PWA...

REM Get current directory
set "CURRENT_DIR=%CD%"

REM Create VBS script to create shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\Desktop\Northen Star PWA.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%CURRENT_DIR%\start-pwa.bat" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%CURRENT_DIR%" >> CreateShortcut.vbs
echo oLink.Description = "Start Northen Star PWA" >> CreateShortcut.vbs
echo oLink.IconLocation = "%CURRENT_DIR%\public\logo.png" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

REM Execute VBS script
cscript CreateShortcut.vbs >nul

REM Clean up
del CreateShortcut.vbs

echo.
echo Desktop shortcut created successfully!
echo You can now double-click "Northen Star PWA" on your desktop to start the app.
echo.
pause
