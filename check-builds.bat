@echo off
echo ========================================
echo Checking Build Status
echo ========================================
echo.

echo Checking Web Build...
if exist "out\index.html" (
    echo ✅ Web build found: out\index.html
) else (
    echo ❌ Web build not found
)

echo.
echo Checking Desktop Builds...
if exist "dist\win-unpacked\Northen Star.exe" (
    echo ✅ Electron build found: dist\win-unpacked\Northen Star.exe
) else (
    echo ❌ Electron build not found
)

if exist "src-tauri\target\release\app.exe" (
    echo ✅ Tauri executable found: src-tauri\target\release\app.exe
) else (
    echo ❌ Tauri executable not found
)

if exist "src-tauri\target\release\bundle\msi\Northen Star_0.1.0_x64_en-US.msi" (
    echo ✅ Tauri MSI installer found: src-tauri\target\release\bundle\msi\Northen Star_0.1.0_x64_en-US.msi
) else (
    echo ❌ Tauri MSI installer not found
)

if exist "src-tauri\target\release\bundle\nsis\Northen Star_0.1.0_x64-setup.exe" (
    echo ✅ Tauri NSIS installer found: src-tauri\target\release\bundle\nsis\Northen Star_0.1.0_x64-setup.exe
) else (
    echo ❌ Tauri NSIS installer not found
)

echo.
echo Checking Android Build...
if exist "android\app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ Android debug APK found: android\app\build\outputs\apk\debug\app-debug.apk
) else (
    echo ❌ Android debug APK not found
)

if exist "android\app\build\outputs\bundle\release\app-release.aab" (
    echo ✅ Android release bundle found: android\app\build\outputs\bundle\release\app-release.aab
) else (
    echo ❌ Android release bundle not found
)

echo.
echo ========================================
echo Build Locations Summary:
echo ========================================
echo Web:              ./out/
echo Desktop (Electron): ./dist/win-unpacked/
echo Desktop (Tauri):   ./src-tauri/target/release/
echo Installers:       ./src-tauri/target/release/bundle/
echo Android:          ./android/app/build/outputs/
echo.
pause
