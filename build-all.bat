@echo off
echo ========================================
echo Building Northen Star - All Platforms
echo ========================================
echo.

echo [1/4] Building Next.js web application...
call npm run build
if %errorlevel% neq 0 (
    echo ERROR: Web build failed!
    pause
    exit /b 1
)
echo ✅ Web build complete: ./out/
echo.

echo [2/4] Building mobile application...
call npm run build:mobile
if %errorlevel% neq 0 (
    echo ERROR: Mobile build failed!
    pause
    exit /b 1
)
echo ✅ Mobile build complete: Capacitor sync done
echo.

echo [3/4] Building Android APK (debug)...
cd android
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ERROR: Android debug build failed!
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✅ Android debug APK: ./android/app/build/outputs/apk/debug/app-debug.apk
echo.

echo [4/5] Building desktop application (Electron unpacked)...
call npm run build
call npx electron-builder --dir
if %errorlevel% neq 0 (
    echo WARNING: Electron packaging failed, but unpacked version may be available
)
echo ✅ Electron app (unpacked): ./dist/win-unpacked/Northen Star.exe
echo.

echo [5/5] Building desktop application (Tauri)...
call npm run tauri:build
if %errorlevel% neq 0 (
    echo ERROR: Tauri build failed!
    pause
    exit /b 1
)
echo ✅ Tauri app: ./src-tauri/target/release/app.exe
echo ✅ Tauri MSI: ./src-tauri/target/release/bundle/msi/Northen Star_0.1.0_x64_en-US.msi
echo ✅ Tauri NSIS: ./src-tauri/target/release/bundle/nsis/Northen Star_0.1.0_x64-setup.exe
echo.

echo ========================================
echo Build Summary:
echo ========================================
echo ✅ Web: ./out/
echo ✅ Desktop (Electron): ./dist/win-unpacked/Northen Star.exe
echo ✅ Desktop (Tauri): ./src-tauri/target/release/app.exe
echo ✅ Installers (MSI): ./src-tauri/target/release/bundle/msi/Northen Star_0.1.0_x64_en-US.msi
echo ✅ Installers (NSIS): ./src-tauri/target/release/bundle/nsis/Northen Star_0.1.0_x64-setup.exe
echo ✅ Android Debug: ./android/app/build/outputs/apk/debug/app-debug.apk
echo.
echo Note: For production builds, you'll need:
echo - Code signing certificates for desktop
echo - Android signing keys for release APK
echo.
pause
