{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "Northen Star", "version": "0.1.0", "identifier": "com.northenstar.app", "build": {"frontendDist": "../out", "devUrl": "http://localhost:3000", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "Northen Star", "width": 1600, "height": 1300, "minWidth": 1000, "minHeight": 700, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}