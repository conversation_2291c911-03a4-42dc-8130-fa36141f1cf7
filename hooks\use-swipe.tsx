"use client"

import type React from "react"

import { useState, useRef, useCallback } from "react"

interface SwipeConfig {
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  minSwipeDistance?: number
  maxSwipeTime?: number
}

interface SwipeState {
  isSwiping: boolean
  swipeDirection: "left" | "right" | null
  swipeProgress: number
}

export function useSwipe(config: SwipeConfig) {
  const { onSwipeLeft, onSwipeRight, minSwipeDistance = 70, maxSwipeTime = 300 } = config

  const [swipeState, setSwipeState] = useState<SwipeState>({
    isSwiping: false,
    swipeDirection: null,
    swipeProgress: 0,
  })

  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null)
  const touchCurrentRef = useRef<{ x: number; y: number } | null>(null)

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0]
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now(),
    }
    touchCurrentRef.current = {
      x: touch.clientX,
      y: touch.clientY,
    }
  }, [])

  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (!touchStartRef.current) return

      const touch = e.touches[0]
      touchCurrentRef.current = {
        x: touch.clientX,
        y: touch.clientY,
      }

      const deltaX = touch.clientX - touchStartRef.current.x
      const deltaY = touch.clientY - touchStartRef.current.y

      // Only handle horizontal swipes (ignore vertical scrolling)
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
        e.preventDefault()

        const direction = deltaX > 0 ? "right" : "left"
        const progress = Math.min(Math.abs(deltaX) / minSwipeDistance, 1)

        setSwipeState({
          isSwiping: true,
          swipeDirection: direction,
          swipeProgress: progress,
        })
      }
    },
    [minSwipeDistance],
  )

  const handleTouchEnd = useCallback(() => {
    if (!touchStartRef.current || !touchCurrentRef.current) {
      setSwipeState({ isSwiping: false, swipeDirection: null, swipeProgress: 0 })
      return
    }

    const deltaX = touchCurrentRef.current.x - touchStartRef.current.x
    const deltaY = touchCurrentRef.current.y - touchStartRef.current.y
    const deltaTime = Date.now() - touchStartRef.current.time

    // Check if it's a valid swipe
    const isHorizontalSwipe = Math.abs(deltaX) > Math.abs(deltaY)
    const isMinDistance = Math.abs(deltaX) >= minSwipeDistance
    const isMaxTime = deltaTime <= maxSwipeTime

    if (isHorizontalSwipe && isMinDistance && isMaxTime) {
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight()
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft()
      }
    }

    // Reset state
    setSwipeState({ isSwiping: false, swipeDirection: null, swipeProgress: 0 })
    touchStartRef.current = null
    touchCurrentRef.current = null
  }, [onSwipeLeft, onSwipeRight, minSwipeDistance, maxSwipeTime])

  return {
    swipeState,
    swipeHandlers: {
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd,
    },
  }
}
