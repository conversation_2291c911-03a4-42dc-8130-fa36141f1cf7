{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 9569764175203256674, "deps": [[3060637413840920116, "proc_macro2", false, 3443190160544583166], [3150220818285335163, "url", false, 6091892177881750386], [4899080583175475170, "semver", false, 716847677502116053], [7170110829644101142, "json_patch", false, 7182569762026392866], [7392050791754369441, "ico", false, 4814854634004614385], [8319709847752024821, "uuid", false, 3745050113768389076], [9689903380558560274, "serde", false, 15667921800837325527], [9857275760291862238, "sha2", false, 5246332839090223334], [10640660562325816595, "syn", false, 13521442368071639308], [10806645703491011684, "thiserror", false, 13552791310023530115], [11050281405049894993, "tauri_utils", false, 13399737882237405111], [12687914511023397207, "png", false, 5582968336642078770], [13077212702700853852, "base64", false, 17794217529810968291], [14132538657330703225, "brotli", false, 15899027199303902825], [15367738274754116744, "serde_json", false, 6579130706674131689], [15622660310229662834, "walkdir", false, 7013720488400282455], [17990358020177143287, "quote", false, 16216200831819806011]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-da1153ccf9184963\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}