import type { TasksData, NotesData, RemindersConfigData, StatisticsData, AppSettingsData, StorageResult } from "./types"

const TASKS_KEY = "daily-planner-tasks"
const NOTES_KEY = "daily-planner-notes"
const REMINDERS_CONFIG_KEY = "daily-planner-reminders-config"
const STATISTICS_KEY = "daily-planner-statistics"
const APP_SETTINGS_KEY = "daily-planner-app-settings"

// Backup keys
const BACKUP_TASKS_KEY = "daily-planner-backup-tasks"
const BACKUP_NOTES_KEY = "daily-planner-backup-notes"
const BACKUP_REMINDERS_CONFIG_KEY = "daily-planner-backup-reminders-config"
const BACKUP_STATISTICS_KEY = "daily-planner-backup-statistics"
const BACKUP_APP_SETTINGS_KEY = "daily-planner-backup-app-settings"

const DATA_VERSION = "1.2.0" // Increment version for structural changes

export class LocalStorageService {
  private static instance: LocalStorageService

  static getInstance(): LocalStorageService {
    if (!LocalStorageService.instance) {
      LocalStorageService.instance = new LocalStorageService()
    }
    return LocalStorageService.instance
  }

  private async saveData<T extends { updated_at: string }>(
    key: string,
    backupKey: string,
    data: T | null,
  ): Promise<StorageResult<T>> {
    try {
      if (data === null) {
        // For clearing data
        localStorage.removeItem(key)
        return { success: true, source: "localStorage" }
      }

      const currentDataStr = localStorage.getItem(key)
      if (currentDataStr) {
        localStorage.setItem(backupKey, currentDataStr) // Create backup
      }

      data.updated_at = new Date().toISOString() // Ensure updated_at is fresh
      localStorage.setItem(key, JSON.stringify({ version: DATA_VERSION, payload: data }))
      return { success: true, data, source: "localStorage" }
    } catch (error) {
      console.error(`❌ Failed to save to localStorage (key: ${key}):`, error)
      await this.restoreFromBackup(key, backupKey) // Attempt restore
      return { success: false, error: this.formatError(error), source: "localStorage" }
    }
  }

  private async loadData<T>(key: string, backupKey: string, defaultValue: T): Promise<StorageResult<T>> {
    try {
      const dataStr = localStorage.getItem(key)
      if (!dataStr) {
        return { success: true, data: defaultValue, source: "localStorage" }
      }
      const storedItem = JSON.parse(dataStr)

      // Basic migration/validation could be added here if storedItem.version !== DATA_VERSION
      if (storedItem.version !== DATA_VERSION) {
        console.warn(`Version mismatch for ${key}. Expected ${DATA_VERSION}, got ${storedItem.version}. Using default.`)
        // Potentially attempt migration or use default. For now, using default.
        // This could also be a point to clear the outdated data or attempt a more complex migration.
        // For simplicity, if version is very old or incompatible, returning default.
        // A more robust migration strategy would be needed for significant changes.
        return { success: true, data: defaultValue, source: "localStorage" }
      }

      return { success: true, data: storedItem.payload as T, source: "localStorage" }
    } catch (error) {
      console.error(`❌ Failed to load from localStorage (key: ${key}):`, error)
      const backupResult = await this.restoreFromBackup<T>(key, backupKey)
      if (backupResult.success) return backupResult
      return { success: false, data: defaultValue, error: this.formatError(error), source: "localStorage" }
    }
  }

  private formatError(error: unknown): string {
    return error instanceof Error ? error.message : "Unknown localStorage error"
  }

  private async restoreFromBackup<T>(key: string, backupKey: string): Promise<StorageResult<T>> {
    try {
      const backupDataStr = localStorage.getItem(backupKey)
      if (backupDataStr) {
        const backupItem = JSON.parse(backupDataStr)
        // Assuming backup is also versioned or compatible
        localStorage.setItem(key, backupDataStr)
        console.log(`✅ Data restored from backup for key ${key}`)
        return { success: true, data: backupItem.payload as T, source: "localStorage" }
      }
      return { success: false, error: "No backup available", source: "localStorage" }
    } catch (error) {
      console.error(`❌ Failed to restore from backup (key: ${key}):`, error)
      return { success: false, error: this.formatError(error), source: "localStorage" }
    }
  }

  // --- Tasks ---
  async saveTasks(data: TasksData | null): Promise<StorageResult<TasksData>> {
    return this.saveData(TASKS_KEY, BACKUP_TASKS_KEY, data)
  }
  async loadTasks(): Promise<StorageResult<TasksData>> {
    return this.loadData(TASKS_KEY, BACKUP_TASKS_KEY, this.getDefaultTasksData())
  }

  // --- Notes ---
  async saveNotes(data: NotesData | null): Promise<StorageResult<NotesData>> {
    return this.saveData(NOTES_KEY, BACKUP_NOTES_KEY, data)
  }
  async loadNotes(): Promise<StorageResult<NotesData>> {
    return this.loadData(NOTES_KEY, BACKUP_NOTES_KEY, this.getDefaultNotesData())
  }

  // --- RemindersConfig ---
  async saveRemindersConfig(data: RemindersConfigData | null): Promise<StorageResult<RemindersConfigData>> {
    return this.saveData(REMINDERS_CONFIG_KEY, BACKUP_REMINDERS_CONFIG_KEY, data)
  }
  async loadRemindersConfig(): Promise<StorageResult<RemindersConfigData>> {
    return this.loadData(REMINDERS_CONFIG_KEY, BACKUP_REMINDERS_CONFIG_KEY, this.getDefaultRemindersConfigData())
  }

  // --- Statistics ---
  async saveStatistics(data: StatisticsData | null): Promise<StorageResult<StatisticsData>> {
    return this.saveData(STATISTICS_KEY, BACKUP_STATISTICS_KEY, data)
  }
  async loadStatistics(): Promise<StorageResult<StatisticsData>> {
    return this.loadData(STATISTICS_KEY, BACKUP_STATISTICS_KEY, this.getDefaultStatisticsData())
  }

  // --- AppSettings ---
  async saveAppSettings(data: AppSettingsData | null): Promise<StorageResult<AppSettingsData>> {
    return this.saveData(APP_SETTINGS_KEY, BACKUP_APP_SETTINGS_KEY, data)
  }
  async loadAppSettings(): Promise<StorageResult<AppSettingsData>> {
    return this.loadData(APP_SETTINGS_KEY, BACKUP_APP_SETTINGS_KEY, this.getDefaultAppSettingsData())
  }

  async clearAll(): Promise<StorageResult<null>> {
    try {
      ;[
        TASKS_KEY,
        NOTES_KEY,
        REMINDERS_CONFIG_KEY,
        STATISTICS_KEY,
        APP_SETTINGS_KEY,
        BACKUP_TASKS_KEY,
        BACKUP_NOTES_KEY,
        BACKUP_REMINDERS_CONFIG_KEY,
        BACKUP_STATISTICS_KEY,
        BACKUP_APP_SETTINGS_KEY,
      ].forEach((key) => localStorage.removeItem(key))
      localStorage.removeItem("daily-planner-sync-status") // Also clear sync status
      console.log("✅ All localStorage data cleared successfully")
      return { success: true, source: "localStorage" }
    } catch (error) {
      console.error("❌ Failed to clear all localStorage data:", error)
      return { success: false, error: this.formatError(error), source: "localStorage" }
    }
  }

  // --- Default Data Providers ---
  private getDefaultTasksData(): TasksData {
    return {
      tasks: [],
      updated_at: new Date(0).toISOString(), // Epoch time for "never updated"
    }
  }
  private getDefaultNotesData(): NotesData {
    const now = new Date().toISOString()
    return {
      notes: [],
      groups: [
        { id: "work", name: "Work", color: "bg-teal-500", createdAt: now, updatedAt: now },
        { id: "personal", name: "Personal", color: "bg-purple-500", createdAt: now, updatedAt: now },
        { id: "ideas", name: "Ideas", color: "bg-amber-500", createdAt: now, updatedAt: now },
        { id: "tasks", name: "Tasks", color: "bg-blue-500", createdAt: now, updatedAt: now },
        { id: "uncategorized", name: "Uncategorized", color: "bg-gray-500", createdAt: now, updatedAt: now },
      ],
      updated_at: new Date(0).toISOString(),
    }
  }
  private getDefaultRemindersConfigData(): RemindersConfigData {
    return {
      text: "• Call dentist\n• Pay electricity bill\n• Buy groceries",
      updated_at: new Date(0).toISOString(),
    }
  }
  private getDefaultStatisticsData(): StatisticsData {
    return {
      weeklyStats: {},
      streak: 0,
      updated_at: new Date(0).toISOString(),
    }
  }
  private getDefaultAppSettingsData(): AppSettingsData {
    return {
      activeTab: "timeline",
      currentQuoteId: 1,
      currentQuoteDate: new Date().toISOString().split("T")[0], // Set to today
      updated_at: new Date(0).toISOString(),
    }
  }
}
