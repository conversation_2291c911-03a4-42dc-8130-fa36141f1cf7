{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 4156303299353468110, "deps": [[376837177317575824, "softbuffer", false, 7921216205907991046], [442785307232013896, "tauri_runtime", false, 10710502723122521326], [3150220818285335163, "url", false, 2321506650773928468], [3722963349756955755, "once_cell", false, 13246113185456952368], [4143744114649553716, "raw_window_handle", false, 8558078080454601299], [5986029879202738730, "log", false, 9278501114978704911], [7752760652095876438, "build_script_build", false, 983413134283678573], [8539587424388551196, "webview2_com", false, 12487170294840819584], [9010263965687315507, "http", false, 12964141729515555854], [11050281405049894993, "tauri_utils", false, 10182267664814218631], [13223659721939363523, "tao", false, 5713053536631184100], [14585479307175734061, "windows", false, 2403646336011865333], [14794439852947137341, "wry", false, 289325485399410077]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-d441b0c035e71685\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}