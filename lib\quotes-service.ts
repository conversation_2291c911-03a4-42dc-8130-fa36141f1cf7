interface Quote {
  id: number
  text: string
  author: string
  translatedText: string
}

class QuotesService {
  private static instance: QuotesService
  private quotes: Quote[] = []
  private isLoading = false
  private lastFetchTime = 0
  private currentQuoteCache: { quote: Quote | null; date: string } | null = null // Add cache
  private readonly QUOTES_URL =
    "https://raw.githubusercontent.com/BenjaminViranin/northen-star-quotes/refs/heads/main/quotes.json"
  private readonly STORAGE_KEY = "daily-planner-quote"
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 hours

  private constructor() {}

  static getInstance(): QuotesService {
    if (!QuotesService.instance) {
      QuotesService.instance = new QuotesService()
    }
    return QuotesService.instance
  }

  async fetchQuotes(): Promise<Quote[]> {
    // Return cached quotes if available and not expired
    if (this.quotes.length > 0 && Date.now() - this.lastFetchTime < this.CACHE_DURATION) {
      return this.quotes
    }

    if (this.isLoading) {
      // Wait for existing fetch to complete
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!this.isLoading && this.quotes.length > 0) {
            clearInterval(checkInterval)
            resolve(this.quotes)
          }
        }, 100)
      })
    }

    try {
      this.isLoading = true
      const response = await fetch(this.QUOTES_URL)
      if (!response.ok) {
        throw new Error(`Failed to fetch quotes: ${response.status}`)
      }
      this.quotes = await response.json()
      this.lastFetchTime = Date.now()
      this.isLoading = false
      return this.quotes
    } catch (error) {
      console.error("Error fetching quotes:", error)
      this.isLoading = false
      // Return empty array if fetch fails
      return []
    }
  }

  async getCurrentQuote(currentQuoteId?: number, currentQuoteDate?: string): Promise<Quote | null> {
    try {
      const today = new Date().toISOString().split("T")[0]

      // Check cache first to prevent flickering
      if (this.currentQuoteCache && this.currentQuoteCache.date === today) {
        return this.currentQuoteCache.quote
      }

      // Use provided parameters or fall back to localStorage
      let storedId = currentQuoteId || 1
      let lastDate = currentQuoteDate || ""

      if (!currentQuoteId || !currentQuoteDate) {
        const storedData = localStorage.getItem(this.STORAGE_KEY)
        if (storedData) {
          const { id, date } = JSON.parse(storedData)
          storedId = id
          lastDate = date
        }
      }

      // Check if we need to advance to the next quote (new day)
      if (today !== lastDate) {
        storedId = this.getNextQuoteId(storedId)
        this.saveCurrentQuoteId(storedId)
      }

      // Fetch quotes if needed
      if (this.quotes.length === 0) {
        await this.fetchQuotes()
      }

      // Find the quote with the current ID
      const quote = this.quotes.find((q) => q.id === storedId)

      // If quote not found (maybe the ID is out of range), use ID 1
      if (!quote && this.quotes.length > 0) {
        const fallbackQuote = this.quotes.find((q) => q.id === 1) || this.quotes[0]
        this.saveCurrentQuoteId(fallbackQuote.id)
        // Cache the result
        this.currentQuoteCache = { quote: fallbackQuote, date: today }
        return fallbackQuote
      }

      // Cache the result
      this.currentQuoteCache = { quote: quote || null, date: today }
      return quote || null
    } catch (error) {
      console.error("Error getting current quote:", error)
      return null
    }
  }

  async getNextQuote(): Promise<Quote | null> {
    try {
      // Clear cache when getting next quote
      this.currentQuoteCache = null

      // Get current quote ID
      const storedData = localStorage.getItem(this.STORAGE_KEY)
      let currentId = 1

      if (storedData) {
        const { id } = JSON.parse(storedData)
        currentId = id
      }

      // Calculate next ID
      const nextId = this.getNextQuoteId(currentId)

      // Fetch quotes if needed
      if (this.quotes.length === 0) {
        await this.fetchQuotes()
      }

      // Find the quote with the next ID
      const quote = this.quotes.find((q) => q.id === nextId)

      // If quote not found, use ID 1
      if (!quote && this.quotes.length > 0) {
        const fallbackQuote = this.quotes.find((q) => q.id === 1) || this.quotes[0]
        this.saveCurrentQuoteId(fallbackQuote.id)
        // Update cache
        const today = new Date().toISOString().split("T")[0]
        this.currentQuoteCache = { quote: fallbackQuote, date: today }
        return fallbackQuote
      }

      // Save the new current ID
      if (quote) {
        this.saveCurrentQuoteId(nextId)
        // Update cache
        const today = new Date().toISOString().split("T")[0]
        this.currentQuoteCache = { quote, date: today }
      }

      return quote || null
    } catch (error) {
      console.error("Error getting next quote:", error)
      return null
    }
  }

  async getQuoteById(id: number): Promise<Quote | null> {
    try {
      // Fetch quotes if needed
      if (this.quotes.length === 0) {
        await this.fetchQuotes()
      }

      // Find the quote with the specified ID
      const quote = this.quotes.find((q) => q.id === id)
      return quote || null
    } catch (error) {
      console.error("Error getting quote by ID:", error)
      return null
    }
  }

  private getNextQuoteId(currentId: number): number {
    // If we have quotes, find the next valid ID
    if (this.quotes.length > 0) {
      // Find the maximum ID in the quotes array
      const maxId = Math.max(...this.quotes.map((q) => q.id))

      // If current ID is at max, wrap around to ID 1
      if (currentId >= maxId) {
        return 1
      }

      // Otherwise, increment to the next ID
      return currentId + 1
    }

    // If we don't have quotes yet, just increment
    return currentId + 1
  }

  private saveCurrentQuoteId(id: number): void {
    try {
      const today = new Date().toISOString().split("T")[0]
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify({ id, date: today }))
    } catch (error) {
      console.error("Error saving quote ID:", error)
    }
  }
}

export const quotesService = QuotesService.getInstance()
