# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# Next.js
/.next/
/out/
.next

# Production builds
/build
/dist
/dist-exe
/dist-final

# React Native / Expo
.expo/
.expo-shared/
expo-env.d.ts

# Android
/android/app/build/
/android/build/
/android/.gradle/
/android/local.properties
/android/app/release/
/android/app/debug/
/android/gradle.properties
/android/keystore.properties
*.keystore
*.jks

# iOS
/ios/build/
/ios/Pods/
/ios/*.xcworkspace
/ios/*.xcuserdata
/ios/DerivedData/
*.ipa

# Capacitor
/capacitor-cordova-android-plugins/
/capacitor-cordova-ios-plugins/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
lerna-debug.log*
electron-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo
next-env.d.ts

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Vercel
.vercel

# Turbo
.turbo

# PWA files
sw.js.map
workbox-*.js
workbox-*.js.map

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.idea
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# OS generated files
*.log
*.tmp
*.temp

# Backup files
*.bak
*.backup
*.old

# Test coverage
coverage/
.coverage
htmlcov/

# Pytest
.pytest_cache/
.cache

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Local development
.local
.vscode/settings.json

# Supabase
.branches
.temp