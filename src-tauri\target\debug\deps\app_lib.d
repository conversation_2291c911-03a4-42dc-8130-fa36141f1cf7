C:\Users\<USER>\Desktop\G_PROG\React Native\northen-star-react\src-tauri\target\debug\deps\app_lib.lib: src\lib.rs C:\Users\<USER>\Desktop\G_PROG\React\ Native\northen-star-react\src-tauri\target\debug\build\app-7e28474c4e6a2bf0\out/67f2123478f25247d10bac4e65275c3e63f903687a82c5b8f883d0e3ff2165c8

C:\Users\<USER>\Desktop\G_PROG\React Native\northen-star-react\src-tauri\target\debug\deps\app_lib.dll: src\lib.rs C:\Users\<USER>\Desktop\G_PROG\React\ Native\northen-star-react\src-tauri\target\debug\build\app-7e28474c4e6a2bf0\out/67f2123478f25247d10bac4e65275c3e63f903687a82c5b8f883d0e3ff2165c8

C:\Users\<USER>\Desktop\G_PROG\React Native\northen-star-react\src-tauri\target\debug\deps\libapp_lib.rlib: src\lib.rs C:\Users\<USER>\Desktop\G_PROG\React\ Native\northen-star-react\src-tauri\target\debug\build\app-7e28474c4e6a2bf0\out/67f2123478f25247d10bac4e65275c3e63f903687a82c5b8f883d0e3ff2165c8

C:\Users\<USER>\Desktop\G_PROG\React Native\northen-star-react\src-tauri\target\debug\deps\app_lib.d: src\lib.rs C:\Users\<USER>\Desktop\G_PROG\React\ Native\northen-star-react\src-tauri\target\debug\build\app-7e28474c4e6a2bf0\out/67f2123478f25247d10bac4e65275c3e63f903687a82c5b8f883d0e3ff2165c8

src\lib.rs:
C:\Users\<USER>\Desktop\G_PROG\React\ Native\northen-star-react\src-tauri\target\debug\build\app-7e28474c4e6a2bf0\out/67f2123478f25247d10bac4e65275c3e63f903687a82c5b8f883d0e3ff2165c8:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=app
# env-dep:OUT_DIR=C:\\Users\\<USER>\\Desktop\\G_PROG\\React Native\\northen-star-react\\src-tauri\\target\\debug\\build\\app-7e28474c4e6a2bf0\\out
