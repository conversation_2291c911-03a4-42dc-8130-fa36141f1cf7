import type { Metadata, Viewport } from "next";
import "./globals.css";
import "./pwa-styles.css";

export const viewport: Viewport = {
	width: "device-width",
	initialScale: 1,
};

export const metadata: Metadata = {
	title: "Northen Star",
	description: "Daily planner and productivity app to guide your daily goals",
	generator: "v0.dev",
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<body>{children}</body>
		</html>
	);
}
