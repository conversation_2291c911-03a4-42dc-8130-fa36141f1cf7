{"name": "Northen Star", "version": "1.0.0", "description": "An Amazing Capacitor App", "author": {"name": "", "email": ""}, "repository": {"type": "git", "url": ""}, "license": "MIT", "main": "build/src/index.js", "scripts": {"build": "tsc && electron-rebuild", "electron:start-live": "node ./live-runner.js", "electron:start": "npm run build && electron --inspect=5858 ./", "electron:pack": "npm run build && electron-builder build --dir -c ./electron-builder.config.json", "electron:make": "npm run build && electron-builder build -c ./electron-builder.config.json -p always"}, "dependencies": {"@capacitor-community/electron": "^5.0.0", "chokidar": "~3.5.3", "electron-is-dev": "~2.0.0", "electron-serve": "~1.1.0", "electron-unhandled": "~4.0.1", "electron-updater": "^5.3.0", "electron-window-state": "^5.0.3"}, "devDependencies": {"electron": "^26.2.2", "electron-builder": "~23.6.0", "electron-rebuild": "^3.2.9", "typescript": "^5.0.4"}, "keywords": ["capacitor", "electron"]}