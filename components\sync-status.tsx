"use client"

import { useState, useEffect } from "react"
import { Cloud, CloudOff, Wifi, WifiOff, AlertCircle, CheckCircle, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { storageService } from "@/lib/storage/storage-service"
import type { SyncStatus } from "@/lib/storage/types"

interface SyncStatusProps {
  userId?: string
  className?: string
}

export function SyncStatusIndicator({ userId, className }: SyncStatusProps) {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    lastSync: "",
    pendingChanges: false,
    syncInProgress: false,
  })
  const [isOnline, setIsOnline] = useState(true)
  const [lastSyncText, setLastSyncText] = useState("")

  useEffect(() => {
    const updateStatus = () => {
      const status = storageService.getSyncStatus()
      const online = storageService.getConnectionStatus()

      setSyncStatus(status)
      setIsOnline(online)

      // Format last sync time
      if (status.lastSync) {
        const lastSyncDate = new Date(status.lastSync)
        const now = new Date()
        const diffMs = now.getTime() - lastSyncDate.getTime()
        const diffMins = Math.floor(diffMs / (1000 * 60))

        if (diffMins < 1) {
          setLastSyncText("Just now")
        } else if (diffMins < 60) {
          setLastSyncText(`${diffMins}m ago`)
        } else {
          const diffHours = Math.floor(diffMins / 60)
          setLastSyncText(`${diffHours}h ago`)
        }
      } else {
        setLastSyncText("Never")
      }
    }

    updateStatus()
    const interval = setInterval(updateStatus, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = () => {
    if (syncStatus.syncInProgress) {
      return <Loader2 className="h-3 w-3 animate-spin text-blue-400" />
    }

    if (!isOnline) {
      return <CloudOff className="h-3 w-3 text-gray-500" />
    }

    if (syncStatus.lastError) {
      return <AlertCircle className="h-3 w-3 text-red-400" />
    }

    if (syncStatus.pendingChanges) {
      return <Cloud className="h-3 w-3 text-yellow-400" />
    }

    return <CheckCircle className="h-3 w-3 text-green-400" />
  }

  const getStatusText = () => {
    if (syncStatus.syncInProgress) {
      return "Syncing..."
    }

    if (!isOnline) {
      return "Offline"
    }

    if (syncStatus.lastError) {
      return "Sync error"
    }

    if (syncStatus.pendingChanges) {
      return "Pending sync"
    }

    return "Synced"
  }

  const getStatusColor = () => {
    if (syncStatus.syncInProgress) {
      return "text-blue-400"
    }

    if (!isOnline) {
      return "text-gray-500"
    }

    if (syncStatus.lastError) {
      return "text-red-400"
    }

    if (syncStatus.pendingChanges) {
      return "text-yellow-400"
    }

    return "text-green-400"
  }

  const handleRetrySync = async () => {
    if (userId && isOnline) {
      try {
        await storageService.sync(userId)
      } catch (error) {
        console.error("Manual sync failed:", error)
      }
    }
  }

  return (
    <div className={cn("flex items-center gap-2 text-xs", className)}>
      <div className="flex items-center gap-1">
        {getStatusIcon()}
        <span className={cn("font-medium", getStatusColor())}>{getStatusText()}</span>
      </div>

      {lastSyncText !== "Never" && <span className="text-gray-500">• {lastSyncText}</span>}

      {syncStatus.lastError && isOnline && (
        <button onClick={handleRetrySync} className="text-blue-400 hover:text-blue-300 underline">
          Retry
        </button>
      )}

      <div className="flex items-center gap-1">
        {isOnline ? <Wifi className="h-3 w-3 text-green-400" /> : <WifiOff className="h-3 w-3 text-gray-500" />}
      </div>
    </div>
  )
}
