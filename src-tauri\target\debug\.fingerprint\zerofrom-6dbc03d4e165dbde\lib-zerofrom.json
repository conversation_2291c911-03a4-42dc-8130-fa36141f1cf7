{"rustc": 16591470773350601817, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 15657897354478470176, "path": 1662370791247447133, "deps": [[4022439902832367970, "zerofrom_derive", false, 9002539160550589391]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-6dbc03d4e165dbde\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}