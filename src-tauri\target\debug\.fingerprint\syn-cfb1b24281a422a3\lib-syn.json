{"rustc": 16591470773350601817, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 2165234757093567422, "deps": [[1988483478007900009, "unicode_ident", false, 12501101981956685924], [3060637413840920116, "proc_macro2", false, 3443190160544583166], [17990358020177143287, "quote", false, 16216200831819806011]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-cfb1b24281a422a3\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}