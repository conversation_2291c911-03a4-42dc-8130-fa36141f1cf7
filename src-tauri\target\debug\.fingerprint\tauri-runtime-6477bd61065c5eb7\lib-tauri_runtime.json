{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 1776179443988225175, "deps": [[442785307232013896, "build_script_build", false, 13502545213734798916], [3150220818285335163, "url", false, 2321506650773928468], [4143744114649553716, "raw_window_handle", false, 8558078080454601299], [7606335748176206944, "dpi", false, 15394093729196786590], [9010263965687315507, "http", false, 12964141729515555854], [9689903380558560274, "serde", false, 10595498013908148355], [10806645703491011684, "thiserror", false, 13552791310023530115], [11050281405049894993, "tauri_utils", false, 10182267664814218631], [14585479307175734061, "windows", false, 2403646336011865333], [15367738274754116744, "serde_json", false, 6112304692101926396], [16727543399706004146, "cookie", false, 7963338587914383062]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-6477bd61065c5eb7\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}