const { app, BrowserWindow, <PERSON>u, shell } = require("electron");
const path = require("path");
const isDev = process.env.NODE_ENV === "development";

// Keep a global reference of the window object
let mainWindow;

function createWindow() {
	// Create the browser window
	mainWindow = new BrowserWindow({
		width: 1200,
		height: 800,
		minWidth: 800,
		minHeight: 600,
		webPreferences: {
			nodeIntegration: false,
			contextIsolation: true,
			enableRemoteModule: false,
			webSecurity: true,
		},
		icon: path.join(__dirname, "public/logo.png"),
		show: false, // Don't show until ready
		titleBarStyle: "default",
		autoHideMenuBar: false,
	});

	// Load the app
	const startUrl = isDev ? "http://localhost:3000" : `file://${path.join(__dirname, "out/index.html")}`;

	mainWindow.loadURL(startUrl);

	// Open DevTools in development
	if (isDev) {
		mainWindow.webContents.openDevTools();
	}

	// Show window when ready to prevent visual flash
	mainWindow.once("ready-to-show", () => {
		mainWindow.show();
	});

	// Handle window closed
	mainWindow.on("closed", () => {
		mainWindow = null;
	});

	// Handle external links
	mainWindow.webContents.setWindowOpenHandler(({ url }) => {
		shell.openExternal(url);
		return { action: "deny" };
	});

	// Prevent navigation to external URLs
	mainWindow.webContents.on("will-navigate", (event, navigationUrl) => {
		const parsedUrl = new URL(navigationUrl);

		if (parsedUrl.origin !== "http://localhost:3000" && parsedUrl.origin !== "file://") {
			event.preventDefault();
			shell.openExternal(navigationUrl);
		}
	});
}

// Create application menu
function createMenu() {
	const template = [
		{
			label: "File",
			submenu: [
				{
					label: "Quit",
					accelerator: process.platform === "darwin" ? "Cmd+Q" : "Ctrl+Q",
					click: () => {
						app.quit();
					},
				},
			],
		},
		{
			label: "Edit",
			submenu: [
				{ role: "undo" },
				{ role: "redo" },
				{ type: "separator" },
				{ role: "cut" },
				{ role: "copy" },
				{ role: "paste" },
				{ role: "selectall" },
			],
		},
		{
			label: "View",
			submenu: [
				{ role: "reload" },
				{ role: "forceReload" },
				{ role: "toggleDevTools" },
				{ type: "separator" },
				{ role: "resetZoom" },
				{ role: "zoomIn" },
				{ role: "zoomOut" },
				{ type: "separator" },
				{ role: "togglefullscreen" },
			],
		},
		{
			label: "Window",
			submenu: [{ role: "minimize" }, { role: "close" }],
		},
	];

	const menu = Menu.buildFromTemplate(template);
	Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(() => {
	createWindow();
	createMenu();

	app.on("activate", () => {
		// On macOS, re-create window when dock icon is clicked
		if (BrowserWindow.getAllWindows().length === 0) {
			createWindow();
		}
	});
});

// Quit when all windows are closed
app.on("window-all-closed", () => {
	// On macOS, keep app running even when all windows are closed
	if (process.platform !== "darwin") {
		app.quit();
	}
});

// Security: Prevent new window creation
app.on("web-contents-created", (event, contents) => {
	contents.on("new-window", (event, navigationUrl) => {
		event.preventDefault();
		shell.openExternal(navigationUrl);
	});
});
