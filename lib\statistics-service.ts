import type { Task } from "@/lib/storage/types"

export interface DailyStats {
  date: string // YYYY-MM-DD format
  categoryPoints: Record<string, number> // category -> points earned that day
  totalPoints: number
}

export interface WeeklyStatsCalculation {
  weeklyStats: Record<string, Record<string, number>> // date -> category -> points
  maxPossiblePoints: Record<string, number> // category -> max points possible per week
}

class StatisticsService {
  private static instance: StatisticsService

  static getInstance(): StatisticsService {
    if (!StatisticsService.instance) {
      StatisticsService.instance = new StatisticsService()
    }
    return StatisticsService.instance
  }

  /**
   * Check if a category should be tracked in statistics
   */
  private shouldTrackCategory(category: string): boolean {
    return category !== "uncategorized"
  }

  /**
   * Calculate maximum possible points per week for each category
   */
  calculateMaxPossiblePoints(tasks: Task[]): Record<string, number> {
    const categoryMaxPoints: Record<string, number> = {}

    tasks.forEach((task) => {
      const category = task.category || "uncategorized"

      // Skip uncategorized tasks for statistics
      if (!this.shouldTrackCategory(category)) {
        return
      }

      const points = task.points || 0

      if (!categoryMaxPoints[category]) {
        categoryMaxPoints[category] = 0
      }

      // Multiply by 7 for weekly maximum (assuming daily tasks)
      categoryMaxPoints[category] += points * 7
    })

    return categoryMaxPoints
  }

  /**
   * Get the start and end dates of a week containing the given date
   */
  getWeekBounds(date: Date): { start: Date; end: Date } {
    const start = new Date(date)
    const dayOfWeek = start.getDay() // 0 = Sunday, 1 = Monday, etc.
    const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek // Make Monday the start of week

    start.setDate(start.getDate() + mondayOffset)
    start.setHours(0, 0, 0, 0)

    const end = new Date(start)
    end.setDate(end.getDate() + 6)
    end.setHours(23, 59, 59, 999)

    return { start, end }
  }

  /**
   * Get the previous week's bounds
   */
  getPreviousWeekBounds(date: Date): { start: Date; end: Date } {
    const currentWeek = this.getWeekBounds(date)
    const previousWeekStart = new Date(currentWeek.start)
    previousWeekStart.setDate(previousWeekStart.getDate() - 7)

    return this.getWeekBounds(previousWeekStart)
  }

  /**
   * Calculate points earned for completed tasks on a specific day
   */
  calculateDailyPoints(tasks: Task[]): DailyStats {
    const today = new Date().toISOString().split("T")[0]
    const categoryPoints: Record<string, number> = {}
    let totalPoints = 0

    tasks.forEach((task) => {
      if (task.completed) {
        const category = task.category || "uncategorized"

        // Skip uncategorized tasks for statistics
        if (!this.shouldTrackCategory(category)) {
          return
        }

        const points = task.points || 0

        if (!categoryPoints[category]) {
          categoryPoints[category] = 0
        }

        categoryPoints[category] += points
        totalPoints += points
      }
    })

    return {
      date: today,
      categoryPoints,
      totalPoints,
    }
  }

  /**
   * Get the last 7 days (including today)
   */
  getLast7Days(): string[] {
    const dates: string[] = []
    const today = new Date()

    for (let i = 0; i < 7; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() - i)
      dates.push(date.toISOString().split("T")[0])
    }

    return dates
  }

  /**
   * Update or add daily stats to weekly stats
   */
  updateWeeklyStats(
    currentWeeklyStats: Record<string, Record<string, number>>,
    dailyStats: DailyStats,
  ): Record<string, Record<string, number>> {
    const updatedWeeklyStats = { ...currentWeeklyStats }

    // Add or update today's stats (only if there are tracked categories)
    if (Object.keys(dailyStats.categoryPoints).length > 0) {
      updatedWeeklyStats[dailyStats.date] = dailyStats.categoryPoints
    }

    // Keep only the last 7 days
    const last7Days = this.getLast7Days()
    const filteredStats: Record<string, Record<string, number>> = {}

    last7Days.forEach((date) => {
      if (updatedWeeklyStats[date]) {
        filteredStats[date] = updatedWeeklyStats[date]
      }
    })

    return filteredStats
  }

  /**
   * Calculate percentages for display
   */
  calculatePercentages(
    weeklyStats: Record<string, Record<string, number>>,
    maxPossiblePoints: Record<string, number>,
  ): Record<string, number> {
    const totalPointsByCategory: Record<string, number> = {}
    const percentages: Record<string, number> = {}

    // Sum up points for each category across all days
    Object.values(weeklyStats).forEach((dayStats) => {
      Object.entries(dayStats).forEach(([category, points]) => {
        if (!totalPointsByCategory[category]) {
          totalPointsByCategory[category] = 0
        }
        totalPointsByCategory[category] += points
      })
    })

    // Calculate percentages
    Object.entries(totalPointsByCategory).forEach(([category, totalPoints]) => {
      const maxPoints = maxPossiblePoints[category] || 0
      percentages[category] = maxPoints > 0 ? Math.round((totalPoints / maxPoints) * 100) : 0
    })

    return percentages
  }

  /**
   * Get weekly stats calculation
   */
  getWeeklyStatsCalculation(
    tasks: Task[],
    currentWeeklyStats: Record<string, Record<string, number>>,
  ): WeeklyStatsCalculation {
    const maxPossiblePoints = this.calculateMaxPossiblePoints(tasks)

    return {
      weeklyStats: currentWeeklyStats,
      maxPossiblePoints,
    }
  }

  /**
   * Get formatted date range string for the last 7 days
   */
  getLast7DaysRangeString(): string {
    const today = new Date()
    const sevenDaysAgo = new Date(today)
    sevenDaysAgo.setDate(today.getDate() - 6)

    const formatDate = (date: Date) => {
      return date.toLocaleDateString("en-US", { month: "short", day: "numeric" }).toUpperCase()
    }

    return `${formatDate(sevenDaysAgo)} - ${formatDate(today)}`
  }
}

export const statisticsService = StatisticsService.getInstance()
