import { LocalStorageService } from "./local-storage";
import { SupabaseStorageService } from "./supabase-storage";
import type {
	TasksData,
	NotesData,
	RemindersConfigData,
	StatisticsData,
	AppSettingsData,
	StorageResult,
	SyncStatus,
	AllAppData,
} from "./types";

type DataKey = keyof AllAppData;

export class StorageService {
	private static instance: StorageService;
	public localStorage: LocalStorageService;
	public supabaseStorage: SupabaseStorageService;
	private isOnline: boolean = typeof navigator !== "undefined" ? navigator.onLine : true;
	private syncStatus: SyncStatus = this.loadSyncStatus();
	private currentUserId: string | null = null;
	private isUserAuthenticated = false;
	private hasLoadedRemoteData = false;

	private constructor() {
		this.localStorage = LocalStorageService.getInstance();
		this.supabaseStorage = SupabaseStorageService.getInstance();
		this.setupConnectionMonitoring();
		this.loadSyncStatus();
	}

	static getInstance(): StorageService {
		if (!StorageService.instance) {
			StorageService.instance = new StorageService();
		}
		return StorageService.instance;
	}

	public setUserAuthentication(userId: string | null, isAuthenticated: boolean): void {
		const wasAuthenticated = this.isUserAuthenticated;
		const previousUserId = this.currentUserId;

		this.currentUserId = userId;
		this.isUserAuthenticated = isAuthenticated;

		console.log(`🔐 User authentication changed:`, {
			previousUserId,
			newUserId: userId,
			wasAuthenticated,
			isAuthenticated,
			userChanged: previousUserId !== userId,
		});

		if (previousUserId !== userId || !isAuthenticated) {
			this.hasLoadedRemoteData = false;
			console.log(`🔄 Reset remote data flag due to user change or logout`);
		}
	}

	private shouldAllowRemoteSave(userId: string | null): boolean {
		const allowed = !!(
			userId &&
			this.isUserAuthenticated &&
			this.currentUserId === userId &&
			this.isOnline &&
			this.hasLoadedRemoteData
		);

		if (!allowed) {
			alert(
				`🚫 Remote save blocked: hasUserId=${!!userId}, isAuth=${this.isUserAuthenticated}, userMatch=${
					this.currentUserId === userId
				}, online=${this.isOnline}, hasLoaded=${this.hasLoadedRemoteData}`
			);
			console.log(`🚫 Remote save blocked:`, {
				hasUserId: !!userId,
				isAuthenticated: this.isUserAuthenticated,
				userMatches: this.currentUserId === userId,
				isOnline: this.isOnline,
				hasLoadedRemoteData: this.hasLoadedRemoteData,
			});
		}

		return allowed;
	}

	private isDefaultData<T extends { updated_at: string }>(data: T, section: DataKey): boolean {
		const epochTime = new Date(0).toISOString();

		if (data.updated_at === epochTime) {
			console.log(`⚠️ ${section} appears to be default data (epoch timestamp)`);
			return true;
		}

		switch (section) {
			case "tasksData":
				const tasksData = data as any;
				return tasksData.tasks?.length === 0;
			case "notesData":
				const notesData = data as any;
				return notesData.notes?.length === 0 && notesData.groups?.length <= 5; // Default groups
			case "remindersConfigData":
				const remindersData = data as any;
				return !remindersData.text || remindersData.text.trim().length === 0;
			case "statisticsData":
				const statsData = data as any;
				return Object.keys(statsData.weeklyStats || {}).length === 0 && statsData.streak === 0;
			case "appSettingsData":
				return false;
			default:
				return false;
		}
	}

	private setupConnectionMonitoring(): void {
		if (typeof window === "undefined") return;

		const updateOnlineStatus = async () => {
			const online = navigator.onLine;
			if (this.isOnline !== online) {
				this.isOnline = online;
				console.log(`🌐 Connection status changed: ${this.isOnline ? "Online" : "Offline"}`);
				if (this.isOnline && this.isUserAuthenticated && this.currentUserId) {
					this.syncAllPending(this.currentUserId);
				}
			}
			this.updateSyncStatusState({ ...this.syncStatus });
		};

		window.addEventListener("online", updateOnlineStatus);
		window.addEventListener("offline", updateOnlineStatus);
		this.isOnline = navigator.onLine;
	}

	private loadSyncStatus(): SyncStatus {
		try {
			const statusStr =
				typeof localStorage !== "undefined" ? localStorage.getItem("daily-planner-sync-status") : null;
			if (statusStr) {
				return JSON.parse(statusStr);
			}
		} catch (e) {
			console.error("Failed to load sync status from localStorage", e);
		}
		return {
			lastSync: new Date(0).toISOString(),
			pendingChanges: {},
			syncInProgress: false,
			lastError: undefined,
		};
	}

	private updateSyncStatusState(newStatus: Partial<SyncStatus>, section?: DataKey): void {
		this.syncStatus = {
			...this.syncStatus,
			...newStatus,
			pendingChanges: {
				...this.syncStatus.pendingChanges,
				...(section && newStatus.pendingChanges !== undefined && newStatus.pendingChanges[section] !== undefined
					? { [section]: newStatus.pendingChanges[section] }
					: {}),
			},
		};
		if (typeof localStorage !== "undefined") {
			localStorage.setItem("daily-planner-sync-status", JSON.stringify(this.syncStatus));
		}
	}

	public getSyncStatus(): SyncStatus {
		return this.syncStatus;
	}

	public getConnectionStatus(): boolean {
		return this.isOnline;
	}

	private async saveDataInternal<T extends { updated_at: string }>(
		section: DataKey,
		data: T,
		userId: string | null,
		localSaveMethod: (d: T) => Promise<StorageResult<T>>,
		remoteSaveMethod?: (id: string, d: T) => Promise<StorageResult<T>>
	): Promise<StorageResult<T>> {
		console.log(`💾 Saving ${section} data:`, {
			userId,
			currentUserId: this.currentUserId,
			isAuthenticated: this.isUserAuthenticated,
			hasLoadedRemoteData: this.hasLoadedRemoteData,
		});

		data.updated_at = new Date().toISOString();
		const localResult = await localSaveMethod(data);

		console.log(`💾 Local save result for ${section}:`, localResult);

		if (!localResult.success) {
			this.updateSyncStatusState({ lastError: `Local save failed for ${section}: ${localResult.error}` });
			return localResult;
		}

		const savedLocalData = localResult.data || data;

		if (this.shouldAllowRemoteSave(userId) && remoteSaveMethod) {
			if (!this.hasLoadedRemoteData && this.isDefaultData(savedLocalData, section)) {
				console.log(`🚫 Blocking save of default ${section} data before remote load`);
				this.updateSyncStatusState({
					pendingChanges: { ...this.syncStatus.pendingChanges, [section]: true },
				});
				return { ...localResult, data: savedLocalData };
			}

			console.log(`☁️ Attempting remote save for ${section}...`);
			this.updateSyncStatusState({ syncInProgress: true });
			try {
				const remoteResult = await remoteSaveMethod(userId!, savedLocalData);
				console.log(`☁️ Remote save result for ${section}:`, remoteResult);

				if (remoteResult.success && remoteResult.data) {
					const serverConfirmedData = remoteResult.data;
					await localSaveMethod(serverConfirmedData);
					this.updateSyncStatusState({
						syncInProgress: false,
						lastError: undefined,
						pendingChanges: { ...this.syncStatus.pendingChanges, [section]: false },
					});
					return { ...remoteResult, data: serverConfirmedData, source: "supabase" };
				} else {
					this.updateSyncStatusState({
						syncInProgress: false,
						lastError: `Remote save failed for ${section}: ${remoteResult.error}`,
						pendingChanges: { ...this.syncStatus.pendingChanges, [section]: true },
					});
				}
			} catch (error) {
				console.error(`❌ Remote save error for ${section}:`, error);
				this.updateSyncStatusState({
					syncInProgress: false,
					lastError: `Remote save error for ${section}: ${
						error instanceof Error ? error.message : String(error)
					}`,
					pendingChanges: { ...this.syncStatus.pendingChanges, [section]: true },
				});
			}
		} else if (userId && !this.isOnline) {
			console.log(`📴 Offline: ${section} saved locally. Pending sync.`);
			this.updateSyncStatusState({ pendingChanges: { ...this.syncStatus.pendingChanges, [section]: true } });
		} else if (userId && this.isUserAuthenticated) {
			console.log(`⏳ ${section} saved locally. Remote save pending authentication/data load.`);
			this.updateSyncStatusState({ pendingChanges: { ...this.syncStatus.pendingChanges, [section]: true } });
		}

		return { ...localResult, data: savedLocalData };
	}

	// Specific save methods
	async saveTasks(data: TasksData, userId: string | null): Promise<StorageResult<TasksData>> {
		return this.saveDataInternal(
			"tasksData",
			data,
			userId,
			(d) => this.localStorage.saveTasks(d),
			this.supabaseStorage.saveTasks.bind(this.supabaseStorage)
		);
	}

	async saveNotes(data: NotesData, userId: string | null): Promise<StorageResult<NotesData>> {
		return this.saveDataInternal(
			"notesData",
			data,
			userId,
			(d) => this.localStorage.saveNotes(d),
			this.supabaseStorage.saveNotes.bind(this.supabaseStorage)
		);
	}

	async saveRemindersConfig(
		data: RemindersConfigData,
		userId: string | null
	): Promise<StorageResult<RemindersConfigData>> {
		return this.saveDataInternal(
			"remindersConfigData",
			data,
			userId,
			(d) => this.localStorage.saveRemindersConfig(d),
			this.supabaseStorage.saveRemindersConfig.bind(this.supabaseStorage)
		);
	}

	async saveStatistics(data: StatisticsData, userId: string | null): Promise<StorageResult<StatisticsData>> {
		return this.saveDataInternal(
			"statisticsData",
			data,
			userId,
			(d) => this.localStorage.saveStatistics(d),
			this.supabaseStorage.saveStatistics.bind(this.supabaseStorage)
		);
	}

	async saveAppSettings(data: AppSettingsData, userId: string | null): Promise<StorageResult<AppSettingsData>> {
		return this.saveDataInternal(
			"appSettingsData",
			data,
			userId,
			(d) => this.localStorage.saveAppSettings(d),
			this.supabaseStorage.saveAppSettings.bind(this.supabaseStorage)
		);
	}

	// Enhanced loadAll method with better error handling and retry logic for notes
	async loadAll(userId: string | null): Promise<AllAppData> {
		console.log(`📥 Loading all data for user:`, {
			userId,
			currentUserId: this.currentUserId,
			isAuthenticated: this.isUserAuthenticated,
			hasLoadedRemoteData: this.hasLoadedRemoteData,
		});

		this.updateSyncStatusState({ syncInProgress: true });
		let overallError: string | undefined = undefined;

		// Load all local data in parallel first for speed
		const [localTasks, localNotes, localReminders, localStats, localSettings] = await Promise.all([
			this.localStorage.loadTasks(),
			this.localStorage.loadNotes(),
			this.localStorage.loadRemindersConfig(),
			this.localStorage.loadStatistics(),
			this.localStorage.loadAppSettings(),
		]);

		console.log(`📱 Local data loaded:`, {
			tasks: localTasks.success ? localTasks.data?.tasks?.length || 0 : "failed",
			notes: localNotes.success ? localNotes.data?.notes?.length || 0 : "failed",
			reminders: localReminders.success ? !!localReminders.data?.text : "failed",
			stats: localStats.success ? Object.keys(localStats.data?.weeklyStats || {}).length : "failed",
			settings: localSettings.success ? !!localSettings.data?.activeTab : "failed",
		});

		// If no user or offline, return local data immediately
		if (!userId || !this.isOnline || !this.isUserAuthenticated) {
			const localData: AllAppData = {
				tasksData: localTasks.data,
				notesData: localNotes.data,
				remindersConfigData: localReminders.data,
				statisticsData: localStats.data,
				appSettingsData: localSettings.data,
			};
			this.updateSyncStatusState({ syncInProgress: false });
			return localData;
		}

		// Load remote data with enhanced retry logic
		try {
			console.log(`☁️ Loading remote data for user: ${userId}`);

			// Load each section individually with specific error handling
			const loadRemoteSection = async (
				sectionName: string,
				loadFunction: () => Promise<StorageResult<any | null>>
			): Promise<StorageResult<any | null>> => {
				let retryCount = 0;
				const maxRetries = sectionName === "notes" ? 5 : 3; // More retries for notes

				while (retryCount < maxRetries) {
					try {
						const result = await loadFunction();
						if (result.success) {
							console.log(`✅ ${sectionName} loaded successfully`);
							return result;
						} else {
							throw new Error(result.error || `Failed to load ${sectionName}`);
						}
					} catch (error) {
						retryCount++;
						console.warn(`⚠️ ${sectionName} load attempt ${retryCount}/${maxRetries} failed:`, error);

						if (retryCount < maxRetries) {
							const delay = sectionName === "notes" ? 2000 : 1000; // Longer delay for notes
							console.log(`🔄 Retrying ${sectionName} load in ${delay}ms...`);
							await new Promise((resolve) => setTimeout(resolve, delay));
						} else {
							console.error(`❌ ${sectionName} loading failed after ${maxRetries} attempts`);
							throw error;
						}
					}
				}
				throw new Error(`Failed to load ${sectionName} after ${maxRetries} attempts`);
			};

			const [remoteTasks, remoteNotes, remoteReminders, remoteStats, remoteSettings] = await Promise.all([
				loadRemoteSection("tasks", () => this.supabaseStorage.loadTasks(userId)),
				loadRemoteSection("notes", () => this.supabaseStorage.loadNotes(userId)),
				loadRemoteSection("reminders", () => this.supabaseStorage.loadRemindersConfig(userId)),
				loadRemoteSection("statistics", () => this.supabaseStorage.loadStatistics(userId)),
				loadRemoteSection("settings", () => this.supabaseStorage.loadAppSettings(userId)),
			]);

			console.log(`☁️ Remote data loaded:`, {
				tasks: remoteTasks.success ? remoteTasks.data?.tasks?.length || 0 : "failed",
				notes: remoteNotes.success ? remoteNotes.data?.notes?.length || 0 : "failed",
				reminders: remoteReminders.success ? !!remoteReminders.data?.text : "failed",
				stats: remoteStats.success ? Object.keys(remoteStats.data?.weeklyStats || {}).length : "failed",
				settings: remoteSettings.success ? !!remoteSettings.data?.activeTab : "failed",
			});

			this.hasLoadedRemoteData = true;

			// Enhanced merge logic with better logging
			const mergeData = <T extends { updated_at: string }>(
				local: T | undefined,
				remote: T | null | undefined,
				sectionName: string
			): T | undefined => {
				if (!local && !remote) {
					console.log(`⚠️ No ${sectionName} data found locally or remotely`);
					return undefined;
				}
				if (!local && remote) {
					console.log(`📥 Using remote ${sectionName} (no local data)`);
					return remote;
				}
				if (local && !remote) {
					console.log(`📱 Using local ${sectionName} (no remote data)`);
					return local;
				}

				const localTime = new Date(local!.updated_at).getTime();
				const remoteTime = new Date(remote!.updated_at).getTime();

				if (Math.abs(localTime - remoteTime) <= 1000) {
					console.log(`📱 Using local ${sectionName} (timestamps close)`);
					return local; // Prefer local if timestamps are close
				}

				const winner = localTime > remoteTime ? local : remote;
				const winnerSource = localTime > remoteTime ? "local" : "remote";
				console.log(
					`📊 Using ${winnerSource} ${sectionName} (${
						winnerSource === "local" ? "newer" : "newer"
					} timestamp)`
				);
				return winner;
			};

			const finalData: AllAppData = {
				tasksData: mergeData(localTasks.data, remoteTasks.data, "tasks"),
				notesData: mergeData(localNotes.data, remoteNotes.data, "notes"),
				remindersConfigData: mergeData(localReminders.data, remoteReminders.data, "reminders"),
				statisticsData: mergeData(localStats.data, remoteStats.data, "statistics"),
				appSettingsData: mergeData(localSettings.data, remoteSettings.data, "appSettings"),
			};

			console.log(`🔄 Final merged data:`, {
				tasks: finalData.tasksData?.tasks?.length || 0,
				notes: finalData.notesData?.notes?.length || 0,
				reminders: !!finalData.remindersConfigData?.text,
				stats: Object.keys(finalData.statisticsData?.weeklyStats || {}).length,
				settings: !!finalData.appSettingsData?.activeTab,
			});

			// Update local storage with merged data in background
			Promise.all([
				finalData.tasksData && this.localStorage.saveTasks(finalData.tasksData),
				finalData.notesData && this.localStorage.saveNotes(finalData.notesData),
				finalData.remindersConfigData && this.localStorage.saveRemindersConfig(finalData.remindersConfigData),
				finalData.statisticsData && this.localStorage.saveStatistics(finalData.statisticsData),
				finalData.appSettingsData && this.localStorage.saveAppSettings(finalData.appSettingsData),
			]).catch(console.warn);

			this.updateSyncStatusState({ syncInProgress: false, lastError: overallError });
			return finalData;
		} catch (e) {
			console.error(`❌ Remote load failed, using local data:`, e);
			overallError = e instanceof Error ? e.message : String(e);

			// Fallback to local data
			const localData: AllAppData = {
				tasksData: localTasks.data,
				notesData: localNotes.data,
				remindersConfigData: localReminders.data,
				statisticsData: localStats.data,
				appSettingsData: localSettings.data,
			};

			this.updateSyncStatusState({ syncInProgress: false, lastError: overallError });
			return localData;
		}
	}

	async syncAllPending(userId: string | null): Promise<boolean> {
		if (!this.shouldAllowRemoteSave(userId)) {
			console.log("Sync skipped: User not authenticated or conditions not met.");
			return false;
		}

		this.updateSyncStatusState({ syncInProgress: true, lastError: undefined });
		let allSynced = true;
		let errorOccurred = false;

		const sectionsToSync: DataKey[] = Object.keys(this.syncStatus.pendingChanges || {}).filter(
			(key) => this.syncStatus.pendingChanges?.[key as DataKey]
		) as DataKey[];

		if (sectionsToSync.length === 0) {
			console.log("No pending changes to sync.");
			this.updateSyncStatusState({ syncInProgress: false });
			return true;
		}

		for (const section of sectionsToSync) {
			try {
				let localDataResult;
				switch (section) {
					case "tasksData":
						localDataResult = await this.localStorage.loadTasks();
						break;
					case "notesData":
						localDataResult = await this.localStorage.loadNotes();
						break;
					case "remindersConfigData":
						localDataResult = await this.localStorage.loadRemindersConfig();
						break;
					case "statisticsData":
						localDataResult = await this.localStorage.loadStatistics();
						break;
					case "appSettingsData":
						localDataResult = await this.localStorage.loadAppSettings();
						break;
					default:
						continue;
				}

				if (localDataResult.success && localDataResult.data) {
					const dataToSync = localDataResult.data as any;

					if (this.isDefaultData(dataToSync, section)) {
						console.log(`⏭️ Skipping sync of default ${section} data`);
						this.updateSyncStatusState({
							pendingChanges: { ...this.syncStatus.pendingChanges, [section]: false },
						});
						continue;
					}

					let saveResult;
					switch (section) {
						case "tasksData":
							saveResult = await this.supabaseStorage.saveTasks(userId!, dataToSync);
							break;
						case "notesData":
							saveResult = await this.supabaseStorage.saveNotes(userId!, dataToSync);
							break;
						case "remindersConfigData":
							saveResult = await this.supabaseStorage.saveRemindersConfig(userId!, dataToSync);
							break;
						case "statisticsData":
							saveResult = await this.supabaseStorage.saveStatistics(userId!, dataToSync);
							break;
						case "appSettingsData":
							saveResult = await this.supabaseStorage.saveAppSettings(userId!, dataToSync);
							break;
					}

					if (saveResult && saveResult.success && saveResult.data) {
						switch (section) {
							case "tasksData":
								await this.localStorage.saveTasks(saveResult.data as TasksData);
								break;
							case "notesData":
								await this.localStorage.saveNotes(saveResult.data as NotesData);
								break;
							case "remindersConfigData":
								await this.localStorage.saveRemindersConfig(saveResult.data as RemindersConfigData);
								break;
							case "statisticsData":
								await this.localStorage.saveStatistics(saveResult.data as StatisticsData);
								break;
							case "appSettingsData":
								await this.localStorage.saveAppSettings(saveResult.data as AppSettingsData);
								break;
						}
						this.updateSyncStatusState({
							pendingChanges: { ...this.syncStatus.pendingChanges, [section]: false },
						});
					} else {
						allSynced = false;
						errorOccurred = true;
						this.updateSyncStatusState({ lastError: `Sync failed for ${section}: ${saveResult?.error}` });
					}
				}
			} catch (e) {
				allSynced = false;
				errorOccurred = true;
				this.updateSyncStatusState({
					lastError: `Sync error for ${section}: ${e instanceof Error ? e.message : String(e)}`,
				});
			}
		}

		this.updateSyncStatusState({
			syncInProgress: false,
			lastSync: errorOccurred ? this.syncStatus.lastSync : new Date().toISOString(),
		});
		return allSynced;
	}

	async syncAll(userId: string | null): Promise<AllAppData> {
		console.log(`🔄 Starting full sync for user:`, userId);

		if (!this.shouldAllowRemoteSave(userId)) {
			console.log("Full sync skipped: User not authenticated or conditions not met.");
			return {
				tasksData: (await this.localStorage.loadTasks()).data,
				notesData: (await this.localStorage.loadNotes()).data,
				remindersConfigData: (await this.localStorage.loadRemindersConfig()).data,
				statisticsData: (await this.localStorage.loadStatistics()).data,
				appSettingsData: (await this.localStorage.loadAppSettings()).data,
			};
		}

		this.updateSyncStatusState({ syncInProgress: true, lastError: undefined });
		const finalData: AllAppData = {};
		let errorOccurred = false;

		const syncSection = async <T extends { updated_at: string }>(
			sectionKey: DataKey,
			sectionName: string,
			localLoad: () => Promise<StorageResult<T>>,
			localSave: (data: T) => Promise<StorageResult<T>>,
			remoteLoad: (id: string) => Promise<StorageResult<T | null>>,
			remoteSave: (id: string, data: T) => Promise<StorageResult<T>>
		): Promise<T | undefined> => {
			try {
				console.log(`🔄 Syncing ${sectionName}...`);

				const localResult = await localLoad();
				const remoteResult = await remoteLoad(userId!);

				const local = localResult.data;
				const remote = remoteResult.data;

				const isLocalDefault = local ? this.isDefaultData(local, sectionKey) : true;
				const isRemoteDefault = remote ? this.isDefaultData(remote, sectionKey) : true;

				console.log(`🔄 ${sectionName} sync comparison:`, {
					hasLocal: !!local,
					hasRemote: !!remote,
					localTimestamp: local?.updated_at,
					remoteTimestamp: remote?.updated_at,
					isLocalDefault,
					isRemoteDefault,
				});

				if (remote && isLocalDefault && !isRemoteDefault) {
					console.log(`⬇️ Using remote ${sectionName} (local is default)`);
					await localSave(remote);
					return remote;
				}

				if (local && !isLocalDefault && isRemoteDefault) {
					console.log(`⬆️ Uploading local ${sectionName} (remote is default)`);
					const uploadResult = await remoteSave(userId!, local);
					if (uploadResult.success && uploadResult.data) {
						await localSave(uploadResult.data);
						return uploadResult.data;
					} else {
						throw new Error(uploadResult.error || `Failed to upload ${sectionKey}`);
					}
				}

				if (local && remote && !isLocalDefault && !isRemoteDefault) {
					if (new Date(local.updated_at) > new Date(remote.updated_at)) {
						console.log(`⬆️ Uploading local ${sectionName} to remote (newer)`);
						const uploadResult = await remoteSave(userId!, local);
						if (uploadResult.success && uploadResult.data) {
							await localSave(uploadResult.data);
							return uploadResult.data;
						} else {
							throw new Error(uploadResult.error || `Failed to upload ${sectionKey}`);
						}
					} else if (new Date(remote.updated_at) > new Date(local.updated_at)) {
						console.log(`⬇️ Downloading remote ${sectionName} to local (newer)`);
						await localSave(remote);
						return remote;
					} else {
						console.log(`✅ ${sectionName} timestamps match, using local`);
						return local;
					}
				}

				if (local && !remote) {
					if (!isLocalDefault) {
						console.log(`⬆️ Uploading local ${sectionName} (no remote data)`);
						const uploadResult = await remoteSave(userId!, local);
						if (uploadResult.success && uploadResult.data) {
							await localSave(uploadResult.data);
							return uploadResult.data;
						} else {
							throw new Error(uploadResult.error || `Failed to upload ${sectionKey} (no remote)`);
						}
					} else {
						console.log(`⚠️ Local ${sectionName} is default, no remote data`);
						return local;
					}
				}

				if (!local && remote) {
					console.log(`⬇️ Downloading remote ${sectionName} (no local data)`);
					await localSave(remote);
					return remote;
				}

				console.log(`⚠️ No ${sectionName} data found anywhere`);
				return undefined;
			} catch (e) {
				console.error(`❌ Error syncing section ${sectionKey}:`, e);
				this.updateSyncStatusState({
					lastError: `Sync error for ${sectionKey}: ${e instanceof Error ? e.message : String(e)}`,
				});
				errorOccurred = true;
				return (await localLoad()).data;
			}
		};

		finalData.tasksData = await syncSection(
			"tasksData",
			"tasks",
			this.localStorage.loadTasks.bind(this.localStorage),
			this.localStorage.saveTasks.bind(this.localStorage),
			this.supabaseStorage.loadTasks.bind(this.supabaseStorage),
			this.supabaseStorage.saveTasks.bind(this.supabaseStorage)
		);
		finalData.notesData = await syncSection(
			"notesData",
			"notes",
			this.localStorage.loadNotes.bind(this.localStorage),
			this.localStorage.saveNotes.bind(this.localStorage),
			this.supabaseStorage.loadNotes.bind(this.supabaseStorage),
			this.supabaseStorage.saveNotes.bind(this.supabaseStorage)
		);
		finalData.remindersConfigData = await syncSection(
			"remindersConfigData",
			"remindersConfig",
			this.localStorage.loadRemindersConfig.bind(this.localStorage),
			this.localStorage.saveRemindersConfig.bind(this.localStorage),
			this.supabaseStorage.loadRemindersConfig.bind(this.supabaseStorage),
			this.supabaseStorage.saveRemindersConfig.bind(this.supabaseStorage)
		);
		finalData.statisticsData = await syncSection(
			"statisticsData",
			"statistics",
			this.localStorage.loadStatistics.bind(this.localStorage),
			this.localStorage.saveStatistics.bind(this.localStorage),
			this.supabaseStorage.loadStatistics.bind(this.supabaseStorage),
			this.supabaseStorage.saveStatistics.bind(this.supabaseStorage)
		);
		finalData.appSettingsData = await syncSection(
			"appSettingsData",
			"appSettings",
			this.localStorage.loadAppSettings.bind(this.localStorage),
			this.localStorage.saveAppSettings.bind(this.localStorage),
			this.supabaseStorage.loadAppSettings.bind(this.supabaseStorage),
			this.supabaseStorage.saveAppSettings.bind(this.supabaseStorage)
		);

		console.log(`🔄 Full sync completed. Final data:`, {
			tasksCount: finalData.tasksData?.tasks?.length || 0,
			notesCount: finalData.notesData?.notes?.length || 0,
			remindersTextLength: finalData.remindersConfigData?.text?.length || 0,
			weeklyStatsKeys: Object.keys(finalData.statisticsData?.weeklyStats || {}),
			appSettingsActiveTab: finalData.appSettingsData?.activeTab,
			errorOccurred,
		});

		this.updateSyncStatusState({
			syncInProgress: false,
			lastSync: errorOccurred ? this.syncStatus.lastSync : new Date().toISOString(),
			pendingChanges: {},
		});
		return finalData;
	}

	async clearAllLocalData(): Promise<void> {
		console.log(`🗑️ Clearing all local data`);
		await this.localStorage.clearAll();
		this.hasLoadedRemoteData = false;
		this.updateSyncStatusState(this.loadSyncStatus());
	}
}

export const storageService = StorageService.getInstance();
