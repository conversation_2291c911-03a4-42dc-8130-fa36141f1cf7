cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=ext/vswhom.cpp
OPT_LEVEL = Some(0)
OUT_DIR = Some(C:\Users\<USER>\Desktop\G_PROG\React Native\northen-star-react\src-tauri\target\debug\build\vswhom-sys-9b636960a0675609\out)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\Users\<USER>\Desktop\G_PROG\React Native\northen-star-react\src-tauri\target\debug\deps;C:\Users\<USER>\Desktop\G_PROG\React Native\northen-star-react\src-tauri\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Users\<USER>\Desktop\G_PROG\React Native\northen-star-react\node_modules\.bin;C:\Users\<USER>\Desktop\G_PROG\React Native\northen-star-react\node_modules\.bin;C:\Users\<USER>\Desktop\G_PROG\React Native\node_modules\.bin;C:\Users\<USER>\Desktop\G_PROG\node_modules\.bin;C:\Users\<USER>\Desktop\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Local\nvm\v20.19.1\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\Desktop\G_PROG\React Native\northen-star-react\node_modules\.bin;C:\Users\<USER>\Desktop\G_PROG\React Native\node_modules\.bin;C:\Users\<USER>\Desktop\G_PROG\node_modules\.bin;C:\Users\<USER>\Desktop\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Local\nvm\v20.19.1\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jre-*********-hotspot\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Java\jdk-17\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\ProgramData\chocolatey\bin;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\.cargo\bin;C:\Program Files\Java\jdk-17\bin;C:\src\flutter\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\heroku\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Users\<USER>\.dotnet\tools;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CXX_x86_64-pc-windows-msvc
CXX_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXX_x86_64_pc_windows_msvc
CXX_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXX
HOST_CXX = None
cargo:rerun-if-env-changed=CXX
CXX = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(false)
cargo:rerun-if-env-changed=CXXFLAGS
CXXFLAGS = None
cargo:rerun-if-env-changed=HOST_CXXFLAGS
HOST_CXXFLAGS = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64_pc_windows_msvc
CXXFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64-pc-windows-msvc
CXXFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
vswhom.cpp
ext/vswhom.cpp(213): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
ext/vswhom.cpp(216): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
ext/vswhom.cpp(431): warning C4456: declaration of 'hr' hides previous local declaration
ext/vswhom.cpp(418): note: see declaration of 'hr'
ext/vswhom.cpp(459): warning C4244: 'argument': conversion from 'LONGLONG' to 'int', possible loss of data
ext/vswhom.cpp(502): warning C4456: declaration of 'rc' hides previous local declaration
ext/vswhom.cpp(410): note: see declaration of 'rc'
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64
cargo:rustc-link-lib=static=vswhom
cargo:rustc-link-search=native=C:\Users\<USER>\Desktop\G_PROG\React Native\northen-star-react\src-tauri\target\debug\build\vswhom-sys-9b636960a0675609\out
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64-pc-windows-msvc
CXXSTDLIB_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64_pc_windows_msvc
CXXSTDLIB_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXXSTDLIB
HOST_CXXSTDLIB = None
cargo:rerun-if-env-changed=CXXSTDLIB
CXXSTDLIB = None
cargo:rustc-link-lib=dylib=OleAut32
cargo:rustc-link-lib=dylib=Ole32
