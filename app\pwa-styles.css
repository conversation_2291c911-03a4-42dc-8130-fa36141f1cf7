/* PWA-specific styles for native desktop appearance */

/* Standalone mode - clean native appearance */
@media (display-mode: standalone) {
	/* Reset browser-specific styling */
	html,
	body {
		margin: 0;
		padding: 0;
		overflow-x: hidden;
		background-color: #0f172a !important;
		font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
	}

	/* Remove browser UI elements */
	body {
		background-color: #0f172a !important;
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;
	}

	/* Remove tap highlights and browser-specific styling */
	* {
		-webkit-tap-highlight-color: transparent;
		-webkit-touch-callout: none;
	}

	/* Ensure app content starts from top */
	main {
		padding-top: 0;
		margin-top: 0;
		min-height: 100vh;
	}

	/* Default window size for desktop */
	@media (min-width: 768px) {
		body {
			min-width: 768px;
			min-height: 600px;
		}
	}
}

/* Mobile layout with safe area support */
@media (max-width: 767px) {
	body {
		font-size: 14px;
	}

	/* Mobile safe area adjustments */
	main {
		padding-top: calc(env(safe-area-inset-top, 0) * 0.5) !important;
		padding-bottom: env(safe-area-inset-bottom, 0) !important;
		padding-left: env(safe-area-inset-left, 0) !important;
		padding-right: env(safe-area-inset-right, 0) !important;
		min-height: 100vh !important;
	}

	/* Mobile-specific container adjustments - Apply constraints to outer container */
	.container {
		padding: 1rem;
		padding-top: calc(1rem + calc(env(safe-area-inset-top, 0) * 0.5));
		padding-bottom: calc(1rem + env(safe-area-inset-bottom, 0));
		height: calc(100vh - calc(env(safe-area-inset-top, 0) * 0.5) - env(safe-area-inset-bottom, 0)) !important;
		max-height: calc(100vh - calc(env(safe-area-inset-top, 0) * 0.5) - env(safe-area-inset-bottom, 0)) !important;
		overflow: hidden; /* Default for reminders/notes */
		display: flex;
		flex-direction: column;
	}

	/* Allow scrolling when timeline tab is active */
	.container.timeline-active {
		overflow: visible !important;
	}

	/* Ensure content doesn't overlap with system UI */
	.daily-planner-container {
		padding-top: calc(env(safe-area-inset-top, 20px) * 0.5);
		padding-bottom: 0;
		padding-left: max(env(safe-area-inset-left, 0), 16px);
		padding-right: max(env(safe-area-inset-right, 0), 16px);
	}

	/* PWA install banner adjustments for mobile */
	.pwa-install-banner {
		top: calc(1rem + env(safe-area-inset-top, 0));
		left: calc(1rem + env(safe-area-inset-left, 0));
		right: calc(1rem + env(safe-area-inset-right, 0));
	}

	/* Non-scrollable tabs for reminders and notes - Remove inner constraints */
	.non-scrollable-tab {
		overflow: hidden !important;
		height: 100% !important;
		flex: 1;
	}

	.non-scrollable-tab * {
		overflow: hidden !important;
	}

	/* Disable scroll on body when reminders or notes tab is active */
	body.no-scroll {
		overflow: hidden !important;
		position: fixed !important;
		width: 100% !important;
		height: 100% !important;
	}

	/* Tab content containers - Let them fill available space naturally */
	.tab-content-reminders,
	.tab-content-notes {
		overflow: hidden !important;
		height: 100% !important;
		flex: 1;
	}

	.tab-content-reminders *,
	.tab-content-notes * {
		overflow: hidden !important;
	}

	/* Allow textarea scrolling within notes and reminders tabs while keeping page non-scrollable */
	.tab-content-notes textarea,
	.tab-content-reminders textarea,
	.scrollable-textarea {
		overflow-y: auto !important;
		overflow-x: hidden !important;
	}

	/* Custom scrollbar styling for textarea - Match dropdown scrollbar style */
	.scrollable-textarea::-webkit-scrollbar {
		width: 6px;
	}

	.scrollable-textarea::-webkit-scrollbar-track {
		background: transparent;
	}

	.scrollable-textarea::-webkit-scrollbar-thumb {
		background-color: rgba(75, 85, 99, 0.5);
		border-radius: 20px;
	}

	.scrollable-textarea::-webkit-scrollbar-thumb:hover {
		background-color: rgba(75, 85, 99, 0.7);
	}

	/* Firefox scrollbar styling for textarea - Match dropdown scrollbar style */
	.scrollable-textarea {
		scrollbar-width: thin;
		scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
	}

	/* Timeline tab should remain scrollable - bottom spacing handled by outer container */
	.tab-content-timeline {
		overflow-y: auto !important;
		height: 100% !important;
		flex: 1;
	}
}

/* Native-style focus indicators */
@media (display-mode: standalone) {
	button:focus,
	input:focus,
	select:focus,
	textarea:focus {
		outline: 2px solid rgba(20, 184, 166, 0.5);
		outline-offset: 2px;
	}

	/* Custom scrollbars for native appearance */
	::-webkit-scrollbar {
		width: 12px;
	}

	::-webkit-scrollbar-track {
		background: rgba(255, 255, 255, 0.1);
	}

	::-webkit-scrollbar-thumb {
		background: rgba(255, 255, 255, 0.3);
		border-radius: 6px;
	}

	::-webkit-scrollbar-thumb:hover {
		background: rgba(255, 255, 255, 0.5);
	}

	/* Prevent text selection for native feel */
	.no-select {
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;
	}
}
