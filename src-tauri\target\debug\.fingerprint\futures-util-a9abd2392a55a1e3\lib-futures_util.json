{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 4630632300485251742, "deps": [[1615478164327904835, "pin_utils", false, 10256132545438497898], [1906322745568073236, "pin_project_lite", false, 10526667286499863094], [5451793922601807560, "slab", false, 6676556099335049217], [7620660491849607393, "futures_core", false, 3307740249941752774], [10565019901765856648, "futures_macro", false, 7846584075288217459], [16240732885093539806, "futures_task", false, 1652464706148267068]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-a9abd2392a55a1e3\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}