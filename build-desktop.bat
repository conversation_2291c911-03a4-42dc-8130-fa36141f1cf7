@echo off
echo Building Northen Star Desktop App...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Clean build directories
echo Cleaning build directories...
if exist "out" (
    echo Removing out directory...
    timeout /t 2 /nobreak >nul
    rmdir /s /q "out" 2>nul
)

if exist "dist" (
    echo Removing dist directory...
    rmdir /s /q "dist" 2>nul
)

echo.
echo Installing dependencies...
npm install

echo.
echo Building Next.js app...
npm run build

if %errorlevel% neq 0 (
    echo Error: Next.js build failed
    pause
    exit /b 1
)

echo.
echo Building Electron app...
npx electron-builder

if %errorlevel% neq 0 (
    echo Error: Electron build failed
    pause
    exit /b 1
)

echo.
echo ✅ Desktop app built successfully!
echo.
echo The installer is located in the 'dist' folder.
echo Look for a file like "Northen Star Setup 0.1.0.exe"
echo.
pause
