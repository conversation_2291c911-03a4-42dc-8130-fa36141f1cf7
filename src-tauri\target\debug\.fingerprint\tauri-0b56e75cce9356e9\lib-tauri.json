{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 14642100191201630401, "deps": [[40386456601120721, "percent_encoding", false, 16810749850373312041], [442785307232013896, "tauri_runtime", false, 10710502723122521326], [1200537532907108615, "url<PERSON><PERSON>n", false, 12525775106203666172], [3150220818285335163, "url", false, 2321506650773928468], [4143744114649553716, "raw_window_handle", false, 8558078080454601299], [4341921533227644514, "muda", false, 14314376688411407422], [4919829919303820331, "serialize_to_javascript", false, 14566317428901822152], [5986029879202738730, "log", false, 9278501114978704911], [7752760652095876438, "tauri_runtime_wry", false, 17154351323940183674], [8539587424388551196, "webview2_com", false, 12487170294840819584], [9010263965687315507, "http", false, 12964141729515555854], [9228235415475680086, "tauri_macros", false, 11405693315026485839], [9538054652646069845, "tokio", false, 836400952631398784], [9689903380558560274, "serde", false, 10595498013908148355], [9920160576179037441, "getrandom", false, 11014397548013596916], [10229185211513642314, "mime", false, 9197720551389168637], [10629569228670356391, "futures_util", false, 9938115859726081793], [10755362358622467486, "build_script_build", false, 4188308480585524706], [10806645703491011684, "thiserror", false, 13552791310023530115], [11050281405049894993, "tauri_utils", false, 10182267664814218631], [11989259058781683633, "dunce", false, 7697725579014557771], [12565293087094287914, "window_vibrancy", false, 6486441553381612177], [12986574360607194341, "serde_repr", false, 4581200243933709727], [13077543566650298139, "heck", false, 7051206041825333927], [13625485746686963219, "anyhow", false, 8405856431614235549], [14585479307175734061, "windows", false, 2403646336011865333], [15367738274754116744, "serde_json", false, 6112304692101926396], [16928111194414003569, "dirs", false, 527921675173805062], [17155886227862585100, "glob", false, 3838655522958775532]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-0b56e75cce9356e9\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}